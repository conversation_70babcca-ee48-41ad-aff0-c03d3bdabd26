# Server
PORT=3000
NODE_ENV=development

# JWT
JWT_SECRET=jwt-secret-key
JWT_REFRESH_SECRET=jwt-refresh-secret-key
JWT_RESET_PASSWORD_SECRET=jwt-reset-password-secret-key
JWT_ACTIVATION_SECRET=jwt-activation-secret-key

# MongoDB
MONGO_URI=mongodb://localhost:27017/moleculer-base

# Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-email-password
MAIL_SECURE=false
SUPPORT_EMAIL=<EMAIL>

# Domain
DOMAIN=http://localhost:8080
BACKEND_BASE_URL=http://localhost:3000

# CORS
CORS_WHITELIST=http://localhost:8080,http://localhost:3000

# SSH Configuration for Asterisk Server
SSH_HOST=***************
SSH_USERNAME=root
SSH_PASSWORD=your_ssh_password
SSH_PORT=22
REMOTE_SOUNDS_DIR=/var/lib/asterisk/sounds

# Asterisk Configuration
ASTERISK_SOUNDS_DIR=/var/lib/asterisk/sounds/voicebot
ARI_URL=http://***************:8088
ARI_USERNAME=ariuser
ARI_PASSWORD=your_ari_password
ARI_APP=voicebot

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
