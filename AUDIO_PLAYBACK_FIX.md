# Audio Playback Fix for ARI Handler Service

## Vấn đề đã được khắc phục

Trư<PERSON> đ<PERSON>, audio từ TTS service không được phát trong cuộc gọi do các vấn đề sau:

1. **Format không tương thích**: TTS tạo file MP3, nhưng Asterisk yêu cầu WAV với sample rate cụ thể
2. **Đường dẫn file sai**: Audio được lưu trong thư mục service, không phải Asterisk sounds directory
3. **Media URI không đúng**: ARI playback sử dụng đường dẫn file không chính xác
4. **Thiếu validation**: Không kiểm tra channel state trước khi play audio

## Các thay đổi đã thực hiện

### 1. Cập nhật TTS Service (`services/openai/tts/tts.service.js`)

#### Thêm audio conversion functionality:
- **FFmpeg integration**: Sử dụng FFmpeg để convert MP3 sang WAV
- **Asterisk-compatible format**: WAV 16-bit PCM, 8kHz, mono
- **Custom sounds directory**: Lưu file vào `/var/lib/asterisk/sounds/custom/`

#### Các method mới:
```javascript
// Convert MP3 to Asterisk-compatible WAV
async convertToAsteriskWav(inputPath, outputPath)

// Get path in Asterisk sounds directory  
getAsteriskSoundPath(filename)

// Generate unique audio filename
generateUniqueAudioFilename(prefix, extension)
```

#### Enhanced textToSpeech action:
- Parameter `forAsterisk: true` để tạo file tương thích Asterisk
- Tự động convert MP3 sang WAV với đúng format
- Trả về `asteriskPath` và `soundName` cho ARI playback

### 2. Cập nhật ARI Handler Service (`services/ari-handler/ariHandler.service.js`)

#### Cải thiện `generateAudio()`:
- Request Asterisk-compatible format từ TTS service
- Enhanced logging để debug
- Proper error handling

#### Cải thiện `playAudio()`:
- **Channel state validation**: Kiểm tra channel state trước khi play
- **Proper media URI**: Sử dụng `sound:custom/filename` format
- **Wait for channel ready**: Delay để đảm bảo channel sẵn sàng
- **Enhanced error handling**: Fallback mechanisms khi audio fail

#### Cập nhật các audio playback calls:
- `startConversation()`: Sử dụng audio result object thay vì filePath
- `processRecording()`: Play AI response với format đúng
- Error handling functions: Consistent audio playback

### 3. Cải thiện Files Service (`services/files/files.service.js`)

#### Enhanced `createFromAudioBuffer()`:
- Support cho multiple audio formats (MP3, WAV)
- Proper mimetype detection
- Better error handling và logging

## Cấu hình môi trường

### Environment Variables:
```bash
# Asterisk sounds directory (optional)
ASTERISK_SOUNDS_DIR=/var/lib/asterisk/sounds/custom

# OpenAI API key (required)
OPENAI_API_KEY=your_openai_api_key
```

### Dependencies:
Đảm bảo các package sau đã được cài đặt:
```bash
npm install fluent-ffmpeg @ffmpeg-installer/ffmpeg
```

## Cách test

### 1. Chạy test script:
```bash
node test-audio-fix.js
```

### 2. Test với cuộc gọi thực:
1. Restart ARI handler service
2. Thực hiện cuộc gọi test
3. Kiểm tra logs để verify audio playback
4. Confirm caller có thể nghe được default greeting

### 3. Kiểm tra file audio:
```bash
# Kiểm tra thư mục Asterisk sounds
ls -la /var/lib/asterisk/sounds/custom/

# Kiểm tra format file WAV
file /var/lib/asterisk/sounds/custom/tts_*.wav
```

## Troubleshooting

### Nếu audio vẫn không play:

1. **Kiểm tra Asterisk sounds directory**:
   ```bash
   sudo mkdir -p /var/lib/asterisk/sounds/custom
   sudo chown asterisk:asterisk /var/lib/asterisk/sounds/custom
   ```

2. **Kiểm tra FFmpeg installation**:
   ```bash
   which ffmpeg
   ffmpeg -version
   ```

3. **Kiểm tra file permissions**:
   ```bash
   sudo chmod 644 /var/lib/asterisk/sounds/custom/*.wav
   ```

4. **Kiểm tra Asterisk logs**:
   ```bash
   sudo tail -f /var/log/asterisk/full
   ```

### Common issues:

- **"File not found"**: Kiểm tra đường dẫn và permissions
- **"Format not supported"**: Verify WAV format (8kHz, 16-bit, mono)
- **"Channel not ready"**: Tăng delay trong playAudio function
- **"TTS service error"**: Kiểm tra OpenAI API key và network

## Monitoring và Logs

### Key log messages để theo dõi:
```
🎵 Generating audio for text: "..."
🎵 TTS audio generated, size: X bytes  
🎵 Audio converted for Asterisk: /path/to/file.wav
🔊 Playing media URI: sound:custom/filename
🔊 Playback started successfully
```

### Error indicators:
```
❌ TTS Error: ...
❌ Error playing audio: ...
❌ Audio conversion failed: ...
```

## Kết quả mong đợi

Sau khi implement các fix này:

1. ✅ **Default greeting audible**: Caller sẽ nghe được lời chào mặc định
2. ✅ **AI responses play correctly**: Phản hồi AI được phát rõ ràng
3. ✅ **Better error handling**: Fallback khi audio fail
4. ✅ **Improved logging**: Dễ dàng debug audio issues
5. ✅ **Asterisk compatibility**: Audio format tương thích hoàn toàn

## Performance Impact

- **TTS generation time**: Tăng ~1-2 giây do audio conversion
- **Storage usage**: File WAV lớn hơn MP3 (~3-4x)
- **CPU usage**: FFmpeg conversion sử dụng CPU tạm thời
- **Memory usage**: Minimal impact

## Future Improvements

1. **Audio caching**: Cache converted audio files
2. **Batch conversion**: Convert multiple files cùng lúc
3. **Quality optimization**: Tune audio parameters cho telephony
4. **Cleanup mechanism**: Tự động xóa old audio files
