[![Moleculer](https://badgen.net/badge/Powered%20by/Moleculer/0e83cd)](https://moleculer.services)

# Moleculer Base Project
This is a [Moleculer](https://moleculer.services/)-based microservices project with authentication, user management, and email functionality. It provides a solid foundation for building secure and scalable applications.

## Features

- **Authentication System**: Complete JWT-based authentication with login, registration, password reset, and account activation
- **User Management**: User profiles, role-based access control, and account management
- **Email Integration**: Email notifications for account actions and password resets
- **API Documentation**: Swagger UI for API documentation and testing
- **Security**: Rate limiting, CORS, input validation, and secure password handling
- **Docker Support**: Ready-to-use Docker and Docker Compose configuration

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or remote)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/your-organization/moleculer-base.git
cd moleculer-base

# Install dependencies
npm install
```

### Configuration

Create a `.env` file in the root directory with the following variables:

```
MONGO_URI=mongodb://localhost:27017/moleculer-base
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-jwt-refresh-secret-key
JWT_RESET_PASSWORD_SECRET=your-jwt-reset-password-secret-key
JWT_ACTIVATION_SECRET=your-jwt-activation-secret-key
DOMAIN=http://localhost:3000
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-email-password
SUPPORT_EMAIL=<EMAIL>

# CORS configuration (comma-separated list of allowed origins)
CORS_WHITELIST=http://localhost:8080,http://localhost:3000
```

### Development

Start the project in development mode:

```bash
npm run dev
```

After starting, open http://localhost:3000/api/docs in your browser to access the Swagger UI documentation.

### Production

Start the project in production mode:

```bash
npm run start
```

### Docker

Start the project with Docker Compose:

```bash
npm run dc:up
```

Stop the Docker Compose stack:

```bash
npm run dc:down
```

## Services

- **api**: API Gateway service with authentication, authorization, and rate limiting
- **users**: User management service with authentication, registration, and profile management
- **swagger**: Swagger UI service for API documentation

## API Documentation

The API documentation is available at http://localhost:3000/api/docs when the server is running.

## NPM Scripts

- `npm run dev`: Start development mode (load all services locally with hot-reload & REPL)
- `npm run start`: Start production mode
- `npm run cli`: Start a CLI and connect to production
- `npm run lint`: Run ESLint
- `npm run ci`: Run continuous test mode with watching
- `npm test`: Run tests & generate coverage report
- `npm run dc:up`: Start the stack with Docker Compose
- `npm run dc:down`: Stop the stack with Docker Compose

## Useful Links

* Moleculer website: https://moleculer.services/
* Moleculer Documentation: https://moleculer.services/docs/0.14/
