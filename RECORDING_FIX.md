# Recording Path Fix - SSH Download Solution

## 🚨 Vấn đề đã được gi<PERSON>i quyết

**Vấn đề**: ARI Handler không thể đọc file recording vì `recordingPath` trỏ đến `/var/spool/asterisk/recording/` trên server Asterisk remote, mà BE không có quyền truy cập trực tiếp.

**Giải pháp**: Implement SSH download để copy file recording từ server Asterisk về local BE server ngay sau khi recording hoàn thành.

## 🔧 Thay đổi đã thực hiện

### 1. **ARI Handler Service** (`services/ari-handler/ariHandler.service.js`)

#### ✅ Thêm SSH dependency:
```javascript
dependencies: ["whisper", "tts", "langchain", "ssh"]
```

#### ✅ Cập nhật `processRecording()` method:
- Copy file từ remote server về local trước khi xử lý
- <PERSON><PERSON> dụng local path cho Whisper transcription
- Cleanup cả local và remote files sau khi xử lý

#### ✅ Thêm `copyRecordingFromAsterisk()` method:
- Handle SSH download với retry mechanism
- Error handling và logging chi tiết

### 2. **SSH Service** (`services/ssh/ssh.service.js`)

#### ✅ Thêm `downloadFileFromAsterisk` action:
- Download file từ remote server về local
- Support retry mechanism
- File verification sau khi download

#### ✅ Thêm `cleanupRemoteFile` action:
- Cleanup file trên remote server
- Prevent disk space accumulation

#### ✅ Thêm helper methods:
- `downloadFileWithRetry()`: Retry logic cho download
- `performSSHDownload()`: Actual SSH download operation

## 🔄 Luồng hoạt động mới

```
📞 Cuộc gọi đến
    ↓
🎙️ Recording được tạo trên server Asterisk
    ↓ (Recording finished event)
📥 SSH download file về local BE server
    ↓
📝 Whisper transcribe từ local file
    ↓
🤖 AI tạo phản hồi
    ↓
🔊 TTS tạo audio + SSH copy sang Asterisk
    ↓
📢 Phát phản hồi
    ↓
🧹 Cleanup local + remote recording files
```

## 📁 File Paths

### Recording Files:
- **Remote (Asterisk)**: `/var/spool/asterisk/recording/session_turn_timestamp.wav`
- **Local (BE)**: `services/ari-handler/recordings/session_turn_timestamp.wav`

### TTS Files:
- **Local (BE)**: `services/openai/tts/storage/temp_files`
- **Remote (Asterisk)**: `/var/lib/asterisk/sounds/voicebot/tts_files.wav`

## 🧪 Testing

### Test Scripts:
1. **`test-recording-flow.js`**: Test complete recording flow
2. **`test-ssh-simple.js`**: Test basic SSH connectivity
3. **`test-tts-ssh.js`**: Test TTS + SSH integration

### Run Tests:
```bash
# Test SSH connection
node test-ssh-simple.js

# Test recording flow
node test-recording-flow.js

# Test TTS integration
node test-tts-ssh.js
```

## 🔒 Security & Performance

### Security:
- SSH credentials stored in environment variables
- Automatic cleanup of sensitive recording files
- File verification after download

### Performance:
- Retry mechanism for network issues
- Parallel processing (download while preparing response)
- Automatic cleanup prevents disk space issues

## 📊 Monitoring

### Logs to watch:
```
📥 Copying recording from Asterisk server
✅ Recording copied successfully
🧹 Local recording file cleaned up
🧹 Remote recording file cleaned up
```

### Error scenarios:
- SSH connection failures → Retry mechanism
- File not found → Error handling with user feedback
- Download timeout → Automatic retry
- Cleanup failures → Warning logs (non-critical)

## 🚀 Benefits

1. **✅ Resolved Path Issue**: BE can now access recording files
2. **✅ Improved Reliability**: Retry mechanism for network issues
3. **✅ Better Resource Management**: Automatic cleanup
4. **✅ Enhanced Security**: No permanent storage of recordings
5. **✅ Scalability**: Can handle multiple concurrent recordings

## 🔧 Configuration

### Environment Variables:
```bash
# SSH Configuration (already configured)
SSH_HOST=***************
SSH_USERNAME=root
SSH_PASSWORD=Ey5WrsNDHynyEb4uawGO6hxWzIvOslTH

# Recording paths
ASTERISK_SOUNDS_DIR=/var/lib/asterisk/sounds/voicebot
```

### Service Dependencies:
- ARI Handler → SSH Service
- SSH Service → node-ssh package
- Whisper Service → Local file access

## ⚠️ Important Notes

1. **Network Dependency**: System now depends on SSH connectivity between BE and Asterisk server
2. **Temporary Storage**: Local recordings are temporary and cleaned up automatically
3. **Error Handling**: If SSH fails, the call will end gracefully with error message
4. **Performance**: Small delay added for file download (typically < 2 seconds)

## 🎯 Next Steps

1. **Production Testing**: Test with real phone calls
2. **Performance Monitoring**: Monitor download times and success rates
3. **Error Alerting**: Setup alerts for SSH failures
4. **Optimization**: Consider caching or pre-downloading for better performance

## 🔍 Troubleshooting

### Common Issues:

1. **SSH Connection Failed**:
   - Check network connectivity
   - Verify SSH credentials
   - Check firewall settings

2. **File Not Found**:
   - Verify Asterisk recording configuration
   - Check recording directory permissions
   - Ensure recording completed successfully

3. **Download Timeout**:
   - Check network latency
   - Increase SSH timeout settings
   - Monitor server load

### Debug Commands:
```bash
# Check remote recording directory
ssh root@*************** "ls -la /var/spool/asterisk/recording/"

# Check local recording directory
ls -la services/ari-handler/recordings/

# Test SSH connectivity
node test-ssh-simple.js
```
