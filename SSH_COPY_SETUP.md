# SSH Copy Setup cho Asterisk TTS

## 📋 Tổng quan

Hệ thống đã được cập nhật để tự động copy file audio từ TTS sang server Asterisk qua SSH ngay sau khi tạo file. Điều này đảm bảo file audio có sẵn trên server Asterisk để play ngay lập tức.

## 🔧 Cấu hình

### 1. Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# SSH Configuration for Asterisk Server
SSH_HOST=***************
SSH_USERNAME=root
SSH_PASSWORD=Ey5WrsNDHynyEb4uawGO6hxWzIvOslTH
SSH_PORT=22
REMOTE_SOUNDS_DIR=/var/lib/asterisk/sounds

# Asterisk Configuration  
ASTERISK_SOUNDS_DIR=/var/lib/asterisk/sounds/voicebot
ARI_URL=http://***************:8088
ARI_USERNAME=ariuser
ARI_PASSWORD=hE8CNBPi9p98Dv9Q
ARI_APP=voicebot

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
```

### 2. Dependencies

Đảm bảo package `node-ssh` đã được cài đặt:

```bash
npm install node-ssh
```

## 🚀 Cách hoạt động

### 1. TTS Service Flow

1. **Tạo audio**: TTS service tạo file MP3 từ text
2. **Convert**: Convert MP3 sang WAV format cho Asterisk
3. **SSH Copy**: Tự động copy file WAV sang server Asterisk
4. **Verify**: Verify file đã được copy thành công
5. **Return**: Trả về thông tin file local và remote

### 2. ARI Handler Integration

- ARI Handler ưu tiên sử dụng file trên server remote khi SSH copy thành công
- Fallback về file local nếu SSH copy thất bại
- Sử dụng multiple URI formats để đảm bảo compatibility

## 📁 Cấu trúc thư mục

```
Server Asterisk:
/var/lib/asterisk/sounds/
├── voicebot/           # TTS files được copy vào đây
│   ├── tts_xxx.wav
│   └── tts_yyy.wav
└── en/                 # Built-in Asterisk sounds
    └── ...

Local Server:
services/openai/tts/storage/  # Temporary files
services/ssh/                 # SSH service
```

## 🧪 Testing

### 1. Test SSH Connection

```bash
node test-ssh-simple.js
```

### 2. Test TTS + SSH Integration

```bash
node test-tts-ssh.js
```

### 3. Test Full System

```bash
node test-ssh-copy.js
```

## 📊 Monitoring

### Logs để theo dõi

1. **TTS Service logs**:
   ```
   🎵 TTS audio generated
   🚀 Copying file to remote Asterisk server...
   ✅ File successfully copied to remote server
   ```

2. **SSH Service logs**:
   ```
   🚀 Starting SSH copy operation
   ✅ SSH copy completed successfully
   ```

3. **ARI Handler logs**:
   ```
   🔊 Using SSH copied file (remote): sound:/var/lib/asterisk/sounds/voicebot/tts_xxx
   ```

### Error Handling

- SSH copy failures không làm crash TTS service
- Fallback về local file nếu remote copy thất bại
- Retry mechanism cho SSH operations
- Detailed error logging

## 🔒 Bảo mật

### Khuyến nghị

1. **SSH Key Authentication**: Thay password bằng SSH key
2. **Restricted User**: Tạo user riêng cho SSH copy thay vì dùng root
3. **Firewall**: Chỉ allow SSH từ IP của application server
4. **File Permissions**: Đảm bảo proper permissions cho audio files

### Setup SSH Key (Optional)

```bash
# Tạo SSH key pair
ssh-keygen -t rsa -b 4096 -f ~/.ssh/asterisk_key

# Copy public key to server
ssh-copy-id -i ~/.ssh/asterisk_key.pub root@***************

# Update environment variables
SSH_PRIVATE_KEY_PATH=/path/to/private/key
# Remove SSH_PASSWORD
```

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Check network connectivity
   - Verify SSH credentials
   - Check firewall settings

2. **Permission Denied**
   - Verify SSH user has write access to sounds directory
   - Check directory permissions: `chmod 755 /var/lib/asterisk/sounds/voicebot`

3. **File Not Found on Playback**
   - Check if SSH copy was successful
   - Verify file path in ARI Handler
   - Check Asterisk sounds directory structure

4. **Slow Performance**
   - Monitor SSH transfer times
   - Consider file size optimization
   - Check network latency

### Debug Commands

```bash
# Check remote directory
ssh root@*************** "ls -la /var/lib/asterisk/sounds/voicebot/"

# Check disk space
ssh root@*************** "df -h /var/lib/asterisk/sounds"

# Test file permissions
ssh root@*************** "touch /var/lib/asterisk/sounds/voicebot/test.txt"
```

## 📈 Performance

### Metrics to Monitor

- SSH transfer time (should be < 2 seconds for typical TTS files)
- File size (WAV files ~100-500KB for typical TTS)
- Success rate of SSH copies
- Fallback usage frequency

### Optimization Tips

1. **Network**: Ensure good network connection between servers
2. **File Size**: Monitor TTS file sizes, optimize if too large
3. **Concurrent Transfers**: SSH service handles multiple transfers
4. **Cleanup**: Regular cleanup of old audio files

## 🎯 Next Steps

1. **Production Deployment**: Deploy với proper environment variables
2. **Monitoring Setup**: Setup alerts cho SSH copy failures
3. **Performance Tuning**: Monitor và optimize transfer times
4. **Security Hardening**: Implement SSH key authentication
5. **Backup Strategy**: Setup backup cho audio files nếu cần

## 📞 Support

Nếu gặp vấn đề:

1. Check logs của TTS và SSH services
2. Run test scripts để verify functionality
3. Check network connectivity và SSH access
4. Verify file permissions trên server Asterisk
