const config = {
  production: {
    secret: process.env.JWT_SECRET || 'jwt-secret-key',
    secretRefresh: process.env.JWT_REFRESH_SECRET || 'jwt-refresh-secret-key',
    JWT_RESET_PASSWORD_SECRET: process.env.JWT_RESET_PASSWORD_SECRET || 'jwt-reset-password-secret-key',
    JWT_ACTIVATION_SECRET: process.env.JWT_ACTIVATION_SECRET || 'jwt-activation-secret-key',
    MONGO_URI: process.env.MONGO_URI || 'mongodb://localhost:27017/moleculer-base',
    port: process.env.PORT || 3000,
    domain: process.env.DOMAIN || 'https://example.com',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    'mail': {
      'host': process.env.MAIL_HOST || 'smtp.gmail.com',
      'port': process.env.MAIL_PORT || 587,
      'secure': process.env.MAIL_SECURE === 'true',
      'auth': {
        'user': process.env.MAIL_USER || '',
        'pass': process.env.MAIL_PASS || '',
      },
      tls: {
        rejectUnauthorized: false
      }
    },
    backend_base_url: process.env.BACKEND_BASE_URL || 'http://localhost:3000',
  },
  development: {
    secret: process.env.JWT_SECRET || 'jwt-secret-key',
    secretRefresh: process.env.JWT_REFRESH_SECRET || 'jwt-refresh-secret-key',
    JWT_RESET_PASSWORD_SECRET: process.env.JWT_RESET_PASSWORD_SECRET || 'jwt-reset-password-secret-key',
    JWT_ACTIVATION_SECRET: process.env.JWT_ACTIVATION_SECRET || 'jwt-activation-secret-key',
    MONGO_URI: process.env.MONGO_URI || 'mongodb://localhost:27017/moleculer-base',
    port: process.env.PORT || 3000,
    domain: process.env.DOMAIN || 'http://localhost:8080',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    'mail': {
      'host': process.env.MAIL_HOST || 'smtp.gmail.com',
      'port': process.env.MAIL_PORT || 587,
      'secure': process.env.MAIL_SECURE === 'true',
      'auth': {
        'user': process.env.MAIL_USER || '',
        'pass': process.env.MAIL_PASS || '',
      },
      tls: {
        rejectUnauthorized: false
      }
    },
    backend_base_url: process.env.BACKEND_BASE_URL || 'http://localhost:3000',
  },
};

exports.getConfig = env => config[env] || config.development;
