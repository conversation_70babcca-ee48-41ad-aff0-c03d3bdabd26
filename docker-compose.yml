version: "3.8"
services:
  api:
    build:
      context: .
    restart: always
    environment:
      PORT: 3000
      NODE_ENV: "production"
      MONGO_URI: 'mongodb://mongo/moleculer-base'
      JWT_SECRET: 'jwt-secret-key'
      JWT_REFRESH_SECRET: 'jwt-refresh-secret-key'
      JWT_RESET_PASSWORD_SECRET: 'jwt-reset-password-secret-key'
      JWT_ACTIVATION_SECRET: 'jwt-activation-secret-key'
      DOMAIN: 'http://localhost:3000'
      MAIL_HOST: 'smtp.example.com'
      MAIL_PORT: 587
      MAIL_USER: ''
      MAIL_PASS: ''
      CORS_WHITELIST: 'http://localhost:8080,http://localhost:3000'
    ports:
      - "3000:3000"
    depends_on:
      - mongo
    volumes:
      - ./:/app
      - /app/node_modules

  mongo:
    image: mongo:4.4
    restart: always
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
