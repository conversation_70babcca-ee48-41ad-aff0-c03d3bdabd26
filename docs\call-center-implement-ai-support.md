# Kế hoạch cải tiến Call Center với AI

## 1. Tổng quan cải tiến

### Tính năng mới:
- **AI Auto-Answer**: <PERSON><PERSON><PERSON> lời tự động bằng AI khi nhân viên không có sẵn
- **Call History**: <PERSON><PERSON><PERSON> lịch sử cuộc gọi với audio recording
- **AI Training**: <PERSON><PERSON><PERSON> AI theo dataset GHVN
- **Audio Processing**: Xử lý audio real-time và lưu trữ

### Kiến trúc tổng quan:
```
Frontend (React) → Backend (Node.js Moleculer) → MongoDB → AI Services
                                ↓
                        Audio Storage → /upload/file
```

## 2. Database Schema (MongoDB)

### Collection: `call_history`
```javascript
{
  _id: ObjectId,
  callId: String, // UUID duy nhất
  sipSessionId: String, // ID từ SIP session
  callerNumber: String, // Số người gọi
  receiverNumber: String, // Số người nhận
  callType: String, // 'incoming' | 'outgoing'
  callStatus: String, // 'completed' | 'missed' | 'rejected' | 'ai_answered'
  startTime: Date, // Thời gian bắt đầu cuộc gọi
  endTime: Date, // Thời gian kết thúc
  duration: Number, // Thời lượng (giây)
  audioUrl: String, // URL file ghi âm
  audioFileId: String, // ID file trong storage
  audioSize: Number, // Kích thước file (bytes)
  aiHandled: Boolean, // Có được AI xử lý không
  aiTranscript: String, // Transcript từ AI
  aiResponse: String, // Phản hồi của AI
  agentId: String, // ID nhân viên xử lý (nếu có)
  customerInfo: {
    name: String,
    phone: String,
    customerId: String
  },
  metadata: {
    callQuality: String, // 'good' | 'average' | 'poor'
    recordingQuality: String,
    deviceInfo: Object,
    networkInfo: Object
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Collection: `ai_training_data`
```javascript
{
  _id: ObjectId,
  conversationId: String,
  question: String, // Câu hỏi khách hàng
  answer: String, // Câu trả lời mẫu
  category: String, // Danh mục: 'support', 'sales', 'complaint', etc.
  intent: String, // Ý định: 'product_inquiry', 'billing', 'technical_support'
  confidence: Number, // Độ tin cậy (0-1)
  context: Object, // Ngữ cảnh cuộc hội thoại
  source: String, // 'manual' | 'from_call' | 'imported'
  verified: Boolean, // Đã được xác minh chưa
  tags: [String], // Tags để phân loại
  createdAt: Date,
  updatedAt: Date
}
```

### Collection: `ai_configurations`
```javascript
{
  _id: ObjectId,
  name: String, // Tên cấu hình AI
  modelType: String, // 'openai' | 'custom' | 'local'
  modelVersion: String,
  settings: {
    temperature: Number,
    maxTokens: Number,
    systemPrompt: String,
    enableAutoAnswer: Boolean,
    autoAnswerDelay: Number, // Giây chờ trước khi AI trả lời
    confidenceThreshold: Number
  },
  active: Boolean,
  trainingDataVersion: String,
  lastTrained: Date,
  performance: {
    accuracy: Number,
    responseTime: Number,
    customerSatisfaction: Number
  },
  createdAt: Date,
  updatedAt: Date
}
```

## 3. Backend Services (Moleculer)

### Service: `call-history.service.js`
```javascript
const { Service } = require("moleculer");
const DbService = require("moleculer-db");
const MongooseAdapter = require("moleculer-db-adapter-mongoose");
const CallHistory = require("../models/call-history.model");

module.exports = {
  name: "call-history",
  mixins: [DbService],
  adapter: new MongooseAdapter("mongodb://localhost:27017/ghvn"),
  model: CallHistory,

  actions: {
    // Bắt đầu ghi lại cuộc gọi
    startCall: {
      params: {
        sipSessionId: "string",
        callerNumber: "string",
        receiverNumber: "string",
        callType: { type: "enum", values: ["incoming", "outgoing"] }
      },
      async handler(ctx) {
        const callId = this.generateCallId();
        const callRecord = await this.adapter.insert({
          callId,
          sipSessionId: ctx.params.sipSessionId,
          callerNumber: ctx.params.callerNumber,
          receiverNumber: ctx.params.receiverNumber,
          callType: ctx.params.callType,
          callStatus: "connecting",
          startTime: new Date(),
          aiHandled: false,
          createdAt: new Date()
        });

        return { callId, record: callRecord };
      }
    },

    // Kết thúc cuộc gọi và upload audio
    endCall: {
      params: {
        callId: "string",
        audioBlob: "any", // Base64 hoặc Buffer
        callStatus: { type: "enum", values: ["completed", "rejected", "missed"] }
      },
      async handler(ctx) {
        const { callId, audioBlob, callStatus } = ctx.params;

        // Upload audio file
        const audioUpload = await ctx.call("file.upload", {
          file: audioBlob,
          filename: `call_${callId}_${Date.now()}.wav`,
          folder: "call-recordings"
        });

        // Update call record
        const updated = await this.adapter.updateById(callId, {
          endTime: new Date(),
          callStatus,
          audioUrl: audioUpload.url,
          audioFileId: audioUpload.fileId,
          audioSize: audioUpload.size,
          duration: this.calculateDuration(callId),
          updatedAt: new Date()
        });

        return updated;
      }
    },

    // Lấy lịch sử cuộc gọi
    getCallHistory: {
      params: {
        page: { type: "number", default: 1 },
        limit: { type: "number", default: 20 },
        agentId: "string|optional",
        dateFrom: "date|optional",
        dateTo: "date|optional",
        callType: "string|optional"
      },
      async handler(ctx) {
        const { page, limit, agentId, dateFrom, dateTo, callType } = ctx.params;

        const query = {};
        if (agentId) query.agentId = agentId;
        if (callType) query.callType = callType;
        if (dateFrom || dateTo) {
          query.startTime = {};
          if (dateFrom) query.startTime.$gte = new Date(dateFrom);
          if (dateTo) query.startTime.$lte = new Date(dateTo);
        }

        const results = await this.adapter.find({
          query,
          limit: limit,
          offset: (page - 1) * limit,
          sort: { startTime: -1 }
        });

        return {
          data: results,
          total: await this.adapter.count({ query }),
          page,
          limit
        };
      }
    }
  },

  methods: {
    generateCallId() {
      return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    calculateDuration(callId) {
      // Tính toán thời lượng cuộc gọi
      // Implementation tùy thuộc vào cách lưu trữ thời gian
    }
  }
};
```

### Service: `ai-assistant.service.js`
```javascript
const { Service } = require("moleculer");
const OpenAI = require("openai");

module.exports = {
  name: "ai-assistant",

  settings: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: "gpt-4"
    }
  },

  actions: {
    // Xử lý cuộc gọi tự động bằng AI
    handleAutoAnswer: {
      params: {
        callId: "string",
        audioTranscript: "string",
        customerInfo: "object|optional"
      },
      async handler(ctx) {
        const { callId, audioTranscript, customerInfo } = ctx.params;

        try {
          // Lấy cấu hình AI hiện tại
          const aiConfig = await ctx.call("ai-config.getActive");

          // Tạo context cho AI
          const context = this.buildContext(customerInfo, audioTranscript);

          // Gọi AI để tạo phản hồi
          const aiResponse = await this.generateResponse(context, aiConfig);

          // Lưu vào lịch sử cuộc gọi
          await ctx.call("call-history.updateAiHandling", {
            callId,
            aiHandled: true,
            aiTranscript: audioTranscript,
            aiResponse: aiResponse.text
          });

          return {
            success: true,
            response: aiResponse.text,
            audioUrl: await this.textToSpeech(aiResponse.text),
            confidence: aiResponse.confidence
          };

        } catch (error) {
          this.logger.error("AI auto-answer failed:", error);
          return {
            success: false,
            error: error.message,
            fallbackMessage: "Xin lỗi, hiện tại tôi không thể xử lý yêu cầu của bạn. Vui lòng chờ nhân viên sẽ hỗ trợ bạn."
          };
        }
      }
    },

    // Huấn luyện AI với dữ liệu mới
    trainWithData: {
      params: {
        trainingData: "array"
      },
      async handler(ctx) {
        const { trainingData } = ctx.params;

        // Validate và xử lý dữ liệu huấn luyện
        const processedData = await this.processTrainingData(trainingData);

        // Lưu vào database
        await ctx.call("ai-training-data.createMany", {
          entities: processedData
        });

        // Trigger re-training (có thể bất đồng bộ)
        this.broker.emit("ai.retrain.requested", {
          dataVersion: Date.now().toString()
        });

        return {
          success: true,
          processedCount: processedData.length,
          message: "Dữ liệu huấn luyện đã được thêm thành công"
        };
      }
    },

    // Chuyển đổi text thành speech
    textToSpeech: {
      params: {
        text: "string",
        voice: { type: "string", default: "vi-VN-Standard-A" }
      },
      async handler(ctx) {
        // Implement TTS service (Google Cloud TTS, Azure, hoặc local)
        // Return URL của file audio được tạo
      }
    }
  },

  methods: {
    async generateResponse(context, aiConfig) {
      const openai = new OpenAI({
        apiKey: this.settings.openai.apiKey
      });

      const completion = await openai.chat.completions.create({
        model: aiConfig.modelVersion || "gpt-4",
        messages: [
          {
            role: "system",
            content: aiConfig.settings.systemPrompt || this.getDefaultSystemPrompt()
          },
          {
            role: "user",
            content: context
          }
        ],
        temperature: aiConfig.settings.temperature || 0.7,
        max_tokens: aiConfig.settings.maxTokens || 500
      });

      return {
        text: completion.choices[0].message.content,
        confidence: completion.choices[0].finish_reason === 'stop' ? 0.9 : 0.7
      };
    },

    buildContext(customerInfo, transcript) {
      let context = `Khách hàng nói: "${transcript}"`;

      if (customerInfo) {
        context += `\nThông tin khách hàng: ${JSON.stringify(customerInfo)}`;
      }

      return context;
    },

    getDefaultSystemPrompt() {
      return `Bạn là trợ lý AI của GHVN CSKH. Nhiệm vụ của bạn là:
1. Trả lời các câu hỏi của khách hàng một cách chuyên nghiệp và thân thiện
2. Cung cấp thông tin chính xác về sản phẩm/dịch vụ
3. Hướng dẫn khách hàng giải quyết các vấn đề cơ bản
4. Chuyển tiếp cho nhân viên nếu vấn đề phức tạp
5. Luôn lịch sự và tôn trọng khách hàng

Trả lời bằng tiếng Việt, ngắn gọn và dễ hiểu.`;
    },

    async processTrainingData(rawData) {
      return rawData.map(item => ({
        question: item.question,
        answer: item.answer,
        category: item.category || 'general',
        intent: item.intent || 'unknown',
        confidence: item.confidence || 0.8,
        source: 'manual',
        verified: false,
        createdAt: new Date()
      }));
    }
  },

  events: {
    "ai.retrain.requested"(payload) {
      // Xử lý việc huấn luyện lại AI
      this.logger.info("AI retraining requested:", payload);
      // Implement retraining logic
    }
  }
};
```

## 4. Frontend Updates

### Enhanced CallInterface.tsx
```typescript
interface CallInterfaceProps {
  // ... existing props
  enableAI?: boolean;
  aiAutoAnswerDelay?: number;
}

export const CallInterface: React.FC<CallInterfaceProps> = ({
  // ... existing props
  enableAI = true,
  aiAutoAnswerDelay = 10
}) => {
  const [callHistory, setCallHistory] = useState<CallRecord[]>([]);
  const [currentRecording, setCurrentRecording] = useState<MediaRecorder | null>(null);
  const [isAIMode, setIsAIMode] = useState(false);
  const [aiResponse, setAiResponse] = useState<string>('');

  // Start recording when call begins
  const startCallRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        await saveCallRecording(audioBlob);
      };

      mediaRecorder.start();
      setCurrentRecording(mediaRecorder);
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  }, []);

  // Save call recording
  const saveCallRecording = useCallback(async (audioBlob: Blob) => {
    if (!callState.currentCall?.callId) return;

    try {
      const formData = new FormData();
      formData.append('file', audioBlob, `call_${callState.currentCall.callId}.wav`);

      const response = await fetch('/upload/file', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await response.json();

      // Update call history
      await fetch('/api/call-history/end-call', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          callId: callState.currentCall.callId,
          audioUrl: uploadResult.url,
          callStatus: 'completed'
        })
      });

    } catch (error) {
      console.error('Failed to save recording:', error);
    }
  }, [callState.currentCall]);

  // Handle AI auto-answer
  const handleAIAutoAnswer = useCallback(async (transcript: string) => {
    if (!enableAI || !callState.incomingCall) return;

    try {
      const response = await fetch('/api/ai-assistant/auto-answer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          callId: callState.incomingCall.callId,
          audioTranscript: transcript,
          customerInfo: callState.incomingCall.customerInfo
        })
      });

      const aiResult = await response.json();

      if (aiResult.success) {
        setAiResponse(aiResult.response);
        setIsAIMode(true);

        // Play AI response audio
        if (aiResult.audioUrl) {
          const audio = new Audio(aiResult.audioUrl);
          audio.play();
        }
      }
    } catch (error) {
      console.error('AI auto-answer failed:', error);
    }
  }, [enableAI, callState.incomingCall]);

  // Load call history
  const loadCallHistory = useCallback(async () => {
    try {
      const response = await fetch('/api/call-history');
      const data = await response.json();
      setCallHistory(data.data);
    } catch (error) {
      console.error('Failed to load call history:', error);
    }
  }, []);

  useEffect(() => {
    loadCallHistory();
  }, [loadCallHistory]);

  // Auto-start recording when call connects
  useEffect(() => {
    if (callState.status === 'connected' && !currentRecording) {
      startCallRecording();
    }
  }, [callState.status, currentRecording, startCallRecording]);

  // Stop recording when call ends
  useEffect(() => {
    if (callState.status === 'idle' && currentRecording) {
      currentRecording.stop();
      setCurrentRecording(null);
    }
  }, [callState.status, currentRecording]);

  return (
    <div className="call-interface">
      {/* ... existing UI */}

      {/* AI Mode Indicator */}
      {isAIMode && (
        <div className="ai-mode-indicator">
          <div className="ai-status">
            <span>🤖 AI đang xử lý cuộc gọi</span>
            <Button
              onClick={() => setIsAIMode(false)}
              variant="outline"
              size="sm"
            >
              Chuyển sang thủ công
            </Button>
          </div>
          {aiResponse && (
            <div className="ai-response">
              <strong>AI Response:</strong>
              <p>{aiResponse}</p>
            </div>
          )}
        </div>
      )}

      {/* Call History Panel */}
      <div className="call-history-panel">
        <h3>Lịch sử cuộc gọi</h3>
        <div className="history-list">
          {callHistory.map((call) => (
            <div key={call.callId} className="history-item">
              <div className="call-info">
                <span className="phone">{call.callerNumber}</span>
                <span className="time">{new Date(call.startTime).toLocaleString()}</span>
                <span className={`status ${call.callStatus}`}>{call.callStatus}</span>
              </div>
              {call.audioUrl && (
                <audio controls src={call.audioUrl} className="call-audio">
                  Trình duyệt không hỗ trợ audio
                </audio>
              )}
              {call.aiHandled && (
                <div className="ai-info">
                  <span className="ai-badge">🤖 AI</span>
                  {call.aiResponse && (
                    <p className="ai-transcript">{call.aiResponse}</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
```

## 5. Implementation Roadmap

### Phase 1: Database & Backend Setup (1-2 tuần)
1. ✅ Thiết kế MongoDB schema
2. ✅ Tạo Moleculer services cơ bản
3. ✅ Implement call history API
4. ✅ Setup file upload endpoint

### Phase 2: Audio Recording & Storage (1 tuần)
1. Integrate MediaRecorder API
2. Implement real-time audio capture
3. Setup audio file upload
4. Test audio quality & compression

### Phase 3: AI Integration (2-3 tuần)
1. Setup OpenAI/AI service integration
2. Implement auto-answer logic
3. Add speech-to-text capability
4. Add text-to-speech for AI responses

### Phase 4: Frontend Enhancement (1-2 tuần)
1. Update CallInterface component
2. Add call history UI
3. Implement AI mode toggle
4. Add real-time status indicators

### Phase 5: AI Training & Optimization (2-3 tuần)
1. Create training data management
2. Implement model fine-tuning
3. Add performance monitoring
4. Optimize response times

### Phase 6: Testing & Production (1-2 tuần)
1. Integration testing
2. Performance optimization
3. Security review
4. Production deployment

## 6. Technical Considerations

### Performance
- **Audio Compression**: Sử dụng WebCodecs API cho real-time compression
- **Streaming**: Implement audio streaming thay vì upload toàn bộ file
- **AI Response Time**: Optimize để giảm độ trễ phản hồi AI
- **Database Indexing**: Index các field thường query (phone, date, agentId)

### Security
- **Audio Encryption**: Mã hóa file audio khi lưu trữ
- **API Authentication**: JWT tokens cho tất cả AI API calls
- **Data Privacy**: Tuân thủ quy định bảo mật dữ liệu khách hàng
- **Rate Limiting**: Giới hạn số lượng AI requests

### Scalability
- **Microservices**: Tách AI service thành service riêng
- **Queue System**: Sử dụng Redis/RabbitMQ cho AI processing
- **CDN**: Lưu trữ audio files trên CDN
- **Load Balancing**: Setup multiple AI service instances

## 7. Monitoring & Analytics

### Metrics to Track
- Call volume và duration
- AI response accuracy
- Audio quality scores
- Customer satisfaction ratings
- System performance metrics

### Dashboards
- Real-time call center status
- AI performance analytics
- Historical call trends
- Agent productivity metrics

## 8. Cost Estimation

### AI Services (Monthly)
- OpenAI API: ~$200-500 (depending on usage)
- Text-to-Speech: ~$50-100
- Speech-to-Text: ~$100-200

### Storage
- Audio files: ~$20-50/month
- Database: ~$30-100/month

### Infrastructure
- Additional server resources: ~$100-300/month

**Total estimated cost: $500-1,250/month**