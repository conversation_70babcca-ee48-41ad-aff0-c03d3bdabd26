const i18next = require("i18next");
const nodemailer = require('nodemailer');
const { getConfig } = require('../config/config');
const config = getConfig(process.env.NODE_ENV);

/**
 * Generate email template for password change notification
 * @param {String} fullName - User's full name
 * @param {String} url - URL to the application
 * @returns {String} HTML email template
 */
exports.generateChangePasswordEmail = (fullName, url) => {
  return `<table border="0" cellspacing="0" cellpadding="0" style="max-width:600px">
  <tbody>
  <tr>
    <td>
      <table bgcolor="#4F46E5" width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:332px;max-width:600px;border:1px solid #e0e0e0;border-bottom:0;border-top-left-radius:3px;border-top-right-radius:3px">
        <tbody>
        <tr>
          <td height="72px" colspan="3"></td>
        </tr>
        <tr>
          <td width="32px"></td>
          <td style="font-family:Roboto-Regular,Helvetica,Arial,sans-serif;font-size:24px;color:#ffffff;line-height:1.25">
            <span class="il">${ i18next.t("user_password_change") }</span>
          </td>
          <td width="32px"></td>
        </tr>
        <tr>
          <td height="18px" colspan="3"></td>
        </tr>
        </tbody>
      </table>
    </td>
  </tr>
  <tr>
    <td>
      <table bgcolor="#FAFAFA" width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:332px;max-width:600px;border:1px solid #f0f0f0;border-bottom:1px solid #c0c0c0;border-top:0;border-bottom-left-radius:3px;border-bottom-right-radius:3px;font-family: Roboto-Regular,Helvetica,Arial,sans-serif;">
        <tbody>
        <tr height="16px">
          <td width="32px" rowspan="3"></td>
          <td></td>
          <td width="32px" rowspan="3"></td>
        </tr>
        <tr>
          <td>
            <table style="min-width:300px" border="0" cellspacing="0" cellpadding="0">
              <tbody>
              <tr>
                <td style="font-family:Roboto-Regular,Helvetica,Arial,sans-serif;font-size:13px;color:#202020;line-height:1.5">
                  ${ i18next.t("hello") } ${ fullName }
                </td>
              </tr>
              <tr>
                <td style="font-size:13px;color:#202020;line-height:1.5">
                  <span class="il">${ i18next.t("user_email_password_of") }</span><b>${ i18next.t("app_name") }</b> ${ i18next.t("user_email_password_has_changed") }
                  <br><br>
                  <div>${ i18next.t("email_user_create_html6") } <a href="${ url }">Link</a></div>
                  <br>
                </td>
              </tr>
              <tr height="32px"></tr>
              <tr>
                <td style="font-size:13px;color:#202020;line-height:1.5">
                  ${ i18next.t("user_email_regard") },<br>${ i18next.t("app_name") }
                </td>
              </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr height="32px"></tr>
        </tbody>
      </table>
    </td>
  </tr>
  </tbody>
</table>`;
};

/**
 * Generate email template for password reset
 * @param {String} fullName - User's full name
 * @param {String} resetUrl - URL to reset password
 * @returns {String} HTML email template
 */
exports.generateResetPasswordEmail = (fullName, resetUrl) => {
  return `<table border="0" cellspacing="0" cellpadding="0" style="max-width:600px">
  <tbody>
  <tr>
    <td>
      <table bgcolor="#4F46E5" width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:332px;max-width:600px;border:1px solid #e0e0e0;border-bottom:0;border-top-left-radius:3px;border-top-right-radius:3px">
        <tbody>
        <tr>
          <td height="72px" colspan="3"></td>
        </tr>
        <tr>
          <td width="32px"></td>
          <td style="font-family:Roboto-Regular,Helvetica,Arial,sans-serif;font-size:24px;color:#ffffff;line-height:1.25">
            <span class="il">${ i18next.t("reset_your_password") }</span>
          </td>
          <td width="32px"></td>
        </tr>
        <tr>
          <td height="18px" colspan="3"></td>
        </tr>
        </tbody>
      </table>
    </td>
  </tr>
  <tr>
    <td>
      <table bgcolor="#FAFAFA" width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:332px;max-width:600px;border:1px solid #f0f0f0;border-bottom:1px solid #c0c0c0;border-top:0;border-bottom-left-radius:3px;border-bottom-right-radius:3px;font-family: Roboto-Regular,Helvetica,Arial,sans-serif;">
        <tbody>
        <tr height="16px">
          <td width="32px" rowspan="3"></td>
          <td></td>
          <td width="32px" rowspan="3"></td>
        </tr>
        <tr>
          <td>
            <table style="min-width:300px" border="0" cellspacing="0" cellpadding="0">
              <tbody>
              <tr>
                <td style="font-family:Roboto-Regular,Helvetica,Arial,sans-serif;font-size:13px;color:#202020;line-height:1.5">
                  ${ i18next.t("hello") } ${ fullName }
                </td>
              </tr>
              <tr>
                <td style="font-size:13px;color:#202020;line-height:1.5">
                  <p>${ i18next.t("reset_password_request") }</p>
                  <p>${ i18next.t("reset_password_instructions") }</p>
                  <div style="text-align:center;margin:30px 0;">
                    <a href="${ resetUrl }" style="background-color:#4F46E5;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;">
                      ${ i18next.t("reset_password") }
                    </a>
                  </div>
                  <p>${ i18next.t("reset_password_expire") }</p>
                  <p>${ i18next.t("reset_password_ignore") }</p>
                </td>
              </tr>
              <tr height="32px"></tr>
              <tr>
                <td style="font-size:13px;color:#202020;line-height:1.5">
                  ${ i18next.t("user_email_regard") },<br>${ i18next.t("app_name") }
                </td>
              </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr height="32px"></tr>
        </tbody>
      </table>
    </td>
  </tr>
  </tbody>
</table>`;
};

/**
 * Send email using nodemailer
 * @param {Object} mailOptions - Mail options object
 * @returns {Promise} Promise that resolves when email is sent
 */
exports.sendEmail = (mailOptions) => {
  return new Promise((resolve, reject) => {
    try {
      const transporter = nodemailer.createTransport(config.mail);
      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.error("Email sending error:", error);
          reject(error);
        } else {
          console.log("Email sent successfully:", info.messageId);
          resolve(info);
        }
      });
    } catch (error) {
      console.error("Email configuration error:", error);
      reject(error);
    }
  });
};
