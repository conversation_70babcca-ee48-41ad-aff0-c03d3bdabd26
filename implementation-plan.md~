1. <PERSON><PERSON> tích hiện trạng
   Dự án hiện tại là một ứng dụng Moleculer-based microservices với nhiều service và chức năng. Tôi thấy dự án có các thành phần chính:
   Backend: Sử dụng Moleculer framework
   Frontend: Có vẻ sử dụng React với Redux
   Database: MongoDB
   Các chức năng xác thực: đă<PERSON> nh<PERSON>, đăng ký, quên mật khẩu, đổi mật khẩu, gửi email
   Nhiều service khác liên quan đến các tính năng nghiệp vụ cụ thể
2. Đ<PERSON> xuất clean project
   2.1. Giữ lại các service cốt lõi
   Service Users: Quản lý người dùng, xác thực
   <PERSON>ng nhập, đăng xuất
   Đ<PERSON><PERSON> ký
   Quên mật khẩu, đổi mật khẩu
   X<PERSON><PERSON> thực JWT
   Service API Gateway: Điểm vào của hệ thống
   Service Email: <PERSON><PERSON><PERSON> email thông báo, xác nhận
   Service Organization: <PERSON><PERSON><PERSON><PERSON> lý tổ chức (nếu cần thiết)
   2.2. Loại bỏ các service không cần thiết
   Dựa vào .gitignore và cấu trúc dự án, có nhiều service có thể loại bỏ:
   File, Image, audios, videos, offlinevideos, tools, Report, docxtemplates, microsoft, ipa, RateLimit, dictationandshadowing, speaking...
   2.3. Làm sạch cấu hình và dependencies
   Làm sạch package.json:
   Loại bỏ các dependencies không cần thiết
   Giữ lại các dependencies cần thiết cho xác thực và gửi email
   Làm sạch cấu hình:
   Đơn giản hóa file config.js
   Loại bỏ các cấu hình đặc thù của dự án cũ
   Làm sạch Docker:
   Đơn giản hóa Dockerfile và docker-compose.yml
   2.4. Tạo cấu trúc chuẩn cho base project
   Cấu trúc thư mục:
   Chuẩn hóa API:
   Thiết kế RESTful API chuẩn
   Tài liệu hóa API bằng Swagger
3. Kế hoạch thực hiện
   3.1. Bước 1: Tạo project base mới
   Tạo một project Moleculer mới với cấu trúc chuẩn
   Cấu hình cơ bản cho Moleculer
   3.2. Bước 2: Di chuyển các service cốt lõi
   Di chuyển service Users với các chức năng xác thực
   Di chuyển service Email
   Di chuyển service Organization (nếu cần)
   3.3. Bước 3: Cấu hình và làm sạch
   Làm sạch dependencies
   Cấu hình môi trường (.env)
   Cấu hình database
   3.4. Bước 4: Kiểm thử
   Kiểm thử các chức năng xác thực
   Kiểm thử gửi email
4. Chi tiết kỹ thuật
   4.1. Dependencies cần giữ lại
   4.2. Cấu trúc service Users
   Giữ nguyên cấu trúc hiện tại của service Users với các chức năng:
   login
   register
   forgotPassword
   resetPassword
   changePassword
   logout
   4.3. Cấu trúc service Email
   Tạo một service Email riêng biệt để xử lý việc gửi email:
   Đăng ký
   Quên mật khẩu
   Đổi mật khẩu
   4.4. Cấu hình môi trường
   Tạo file .env.example với các biến môi trường cần thiết:
5. Lợi ích của đề xuất
   Đơn giản hóa: Loại bỏ các thành phần không cần thiết, giúp dự án dễ hiểu và bảo trì hơn
   Tập trung vào cốt lõi: Giữ lại các chức năng xác thực và quản lý tài khoản cần thiết
   Dễ mở rộng: Cấu trúc chuẩn giúp dễ dàng thêm các service mới trong tương lai
   Hiệu suất tốt hơn: Giảm kích thước dự án, giảm dependencies không cần thiết
   Bảo mật tốt hơn: Tập trung vào việc cải thiện bảo mật cho các chức năng xác thực
6. Các bước tiếp theo
   Tạo tài liệu API cho các endpoint xác thực
   Tạo các test case cho các chức năng xác thực
   Tạo hướng dẫn sử dụng và mở rộng dự án
