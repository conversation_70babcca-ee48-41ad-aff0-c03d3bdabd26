#!/bin/bash

# Install Audio Dependencies for ARI Handler Fix
# This script installs required dependencies for audio conversion

echo "🚀 Installing Audio Dependencies for ARI Handler Fix"
echo "=================================================="

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install Node.js and npm first."
    exit 1
fi

echo "📦 Installing FFmpeg dependencies..."

# Install FFmpeg packages
npm install fluent-ffmpeg @ffmpeg-installer/ffmpeg

if [ $? -eq 0 ]; then
    echo "✅ FFmpeg dependencies installed successfully"
else
    echo "❌ Failed to install FFmpeg dependencies"
    exit 1
fi

# Check if FFmpeg is working
echo "🔍 Verifying FFmpeg installation..."
node -e "
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
ffmpeg.setFfmpegPath(ffmpegPath);
console.log('✅ FFmpeg path:', ffmpegPath);
console.log('✅ FFmpeg is ready for audio conversion');
"

if [ $? -eq 0 ]; then
    echo "✅ FFmpeg verification successful"
else
    echo "❌ FFmpeg verification failed"
    exit 1
fi

# Create Asterisk sounds directory if it doesn't exist
echo "📁 Setting up Asterisk sounds directory..."

ASTERISK_SOUNDS_DIR="/var/lib/asterisk/sounds/custom"

if [ ! -d "$ASTERISK_SOUNDS_DIR" ]; then
    echo "📁 Creating Asterisk sounds directory: $ASTERISK_SOUNDS_DIR"
    sudo mkdir -p "$ASTERISK_SOUNDS_DIR"
    
    if [ $? -eq 0 ]; then
        echo "✅ Directory created successfully"
    else
        echo "❌ Failed to create directory. Please run with sudo or create manually."
    fi
    
    # Set proper permissions
    echo "🔐 Setting permissions for Asterisk sounds directory..."
    sudo chown -R asterisk:asterisk "$ASTERISK_SOUNDS_DIR" 2>/dev/null || echo "⚠️ Could not set asterisk ownership (user may not exist)"
    sudo chmod 755 "$ASTERISK_SOUNDS_DIR"
    
    if [ $? -eq 0 ]; then
        echo "✅ Permissions set successfully"
    else
        echo "⚠️ Could not set permissions. Please check manually."
    fi
else
    echo "✅ Asterisk sounds directory already exists"
fi

# Check environment variables
echo "🔍 Checking environment variables..."

if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️ OPENAI_API_KEY environment variable is not set"
    echo "   Please set it in your environment or .env file"
else
    echo "✅ OPENAI_API_KEY is set"
fi

if [ -z "$ASTERISK_SOUNDS_DIR" ]; then
    echo "ℹ️ ASTERISK_SOUNDS_DIR not set, using default: /var/lib/asterisk/sounds/custom"
else
    echo "✅ ASTERISK_SOUNDS_DIR is set to: $ASTERISK_SOUNDS_DIR"
fi

echo ""
echo "🎉 Installation completed!"
echo "=================================================="
echo "✅ Dependencies installed:"
echo "   • fluent-ffmpeg"
echo "   • @ffmpeg-installer/ffmpeg"
echo ""
echo "✅ Directory setup:"
echo "   • $ASTERISK_SOUNDS_DIR"
echo ""
echo "📋 Next steps:"
echo "   1. Restart your ARI handler service"
echo "   2. Run test script: node test-audio-fix.js"
echo "   3. Make a test call to verify audio playback"
echo ""
echo "💡 If you encounter issues, check the troubleshooting section in AUDIO_PLAYBACK_FIX.md"
echo "=================================================="
