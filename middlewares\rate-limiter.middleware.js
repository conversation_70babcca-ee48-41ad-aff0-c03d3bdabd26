"use strict";

/**
 * Rate limiter middleware for Moleculer API Gateway
 * This middleware limits the number of requests a client can make in a given time period
 */

// Simple in-memory store for rate limiting
// In production, you might want to use Redis or another distributed store
const store = new Map();

/**
 * Clean up old rate limit entries
 */
function cleanupStore() {
  const now = Date.now();
  for (const [key, data] of store.entries()) {
    if (now > data.resetTime) {
      store.delete(key);
    }
  }
}

// Run cleanup every minute
setInterval(cleanupStore, 60 * 1000);

/**
 * Create a rate limiter middleware
 * 
 * @param {Object} opts - Rate limiter options
 * @param {Number} opts.window - Time window in seconds
 * @param {Number} opts.limit - Maximum number of requests in the time window
 * @param {Function} opts.getKey - Function to get the key from the request (default: IP address)
 * @returns {Function} Express middleware
 */
module.exports = function createRateLimiterMiddleware(opts = {}) {
  const options = {
    window: 60, // 1 minute
    limit: 60,  // 60 requests per minute
    getKey: (req) => req.headers["x-forwarded-for"] || req.connection.remoteAddress,
    ...opts
  };

  return function rateLimiterMiddleware(req, res, next) {
    // Skip rate limiting for certain paths if needed
    if (options.skipPaths && options.skipPaths.some(path => req.url.startsWith(path))) {
      return next();
    }

    const key = options.getKey(req);
    const now = Date.now();
    
    // Get or create rate limit data for this key
    let data = store.get(key);
    if (!data) {
      data = {
        count: 0,
        resetTime: now + (options.window * 1000)
      };
      store.set(key, data);
    }
    
    // Reset counter if the time window has passed
    if (now > data.resetTime) {
      data.count = 0;
      data.resetTime = now + (options.window * 1000);
    }
    
    // Increment request count
    data.count++;
    
    // Set rate limit headers
    res.setHeader("X-RateLimit-Limit", options.limit);
    res.setHeader("X-RateLimit-Remaining", Math.max(0, options.limit - data.count));
    res.setHeader("X-RateLimit-Reset", Math.ceil(data.resetTime / 1000));
    
    // Check if rate limit is exceeded
    if (data.count > options.limit) {
      res.setHeader("Retry-After", Math.ceil((data.resetTime - now) / 1000));
      res.statusCode = 429;
      res.end(JSON.stringify({
        success: false,
        code: 429,
        message: "Too many requests. Please try again later."
      }));
      return;
    }
    
    next();
  };
};
