"use strict";

const Ajv = require("ajv");
const { MoleculerError } = require("moleculer").Errors;

/**
 * Create a validator middleware
 *
 * @param {Object} schema - JSON Schema for validation
 * @returns {Function} Validator middleware
 */
module.exports = function createValidatorMiddleware(schema) {
  // Create Ajv instance
  const ajv = new Ajv({
    allErrors: true,
    coerceTypes: true,
    useDefaults: true,
    removeAdditional: true
  });

  // Add email format validator
  ajv.addFormat('email', {
    type: 'string',
    validate: (email) => {
      const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return re.test(String(email).toLowerCase());
    }
  });

  // Compile schema
  const validate = ajv.compile(schema);

  /**
   * Validator middleware
   *
   * @param {Object} ctx - Moleculer context
   * @returns {Promise}
   */
  return async function validator(ctx) {
    // Get params from context
    const params = ctx.params;

    // Validate params against schema
    const valid = validate(params);

    if (!valid) {
      // Format validation errors
      const errors = validate.errors.map(err => {
        return {
          field: err.instancePath.substring(1), // Remove leading slash
          message: err.message,
          type: err.keyword
        };
      });

      // Throw validation error
      throw new MoleculerError("Validation error", 422, "VALIDATION_ERROR", errors);
    }

    // Return params (possibly modified by Ajv)
    return params;
  };
};
