"use strict";

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('moleculer-db').MoleculerDB} MoleculerDB  Moleculer's DB Service Schema
 */
const { Errors } = require("moleculer");

module.exports = {
  /**
   * Action Hooks
   */
  hooks: {
    before: {
      /**
       * Register a before hook for the `create` action.
       * It sets a default value for the quantity field.
       *
       * @param {Context} ctx
       */
      create(ctx) {
        ctx.params.creatorId = ctx.meta?.userID?.toString();
        // ctx.params.quantity = 0;
      },
      update(ctx) {
        ctx.params.editorId = ctx.meta?.userID?.toString();
        // ctx.params.quantity = 0;
      },
      find(ctx) {
        // ctx.params.quantity = 0;
        ctx.params.populate = ctx.params.populate || this.settings.populateOptions;
        ctx.params.query = { ...ctx.params.query };
      },
      list(ctx) {
        // ctx.params.quantity = 0;
        ctx.params.populate = ctx.params.populate || this.settings.populateOptions;
        const query = ctx.params.query ? JSON.parse(ctx.params.query) : {};
        ctx.params.query = { ...query, isDeleted: false };

        if (ctx.params.searchFields) {
          ctx.params.query = this.convertSearchFields(ctx.params.searchFields, ctx.params.query);
        }
      },
    },
    after: {
      find(ctx, res) {
        // ctx.params.quantity = 0;
        return res;
      },

      async create(ctx, res) {
        const data = await this.adapter.findById(res._id);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, data);
      },

      async update(ctx, res) {
        const entity = ctx.params;
        let dataExits = await this.adapter.findById(entity.id);
        if (!dataExits) {
          throw new Errors.MoleculerClientError("Data not found", 402);
        }
        const data = await this.adapter.updateById(entity.id, entity);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, data);
      },
    },
    error: {
      "*": function (ctx, err) {
        console.log("ERROR HOOK++++++", err);
        throw err;
      },
    },
  },
  /**
   * Events
   */
  events: {},

  /**
   * Actions
   */
  actions: {
    getAllWithoutPagination: {
      rest: "GET /findAll",
      auth: "required",
      /** @param {Context} ctx */

      async handler(ctx) {
        const { query, populate, searchFields, sort } = ctx.params;
        const parsedQuery = query ? JSON.parse(query) : {};
        const updatedQuery = { ...parsedQuery, isDeleted: { $ne: true } };

        const finalQuery = searchFields ? this.convertSearchFields(searchFields, updatedQuery) : updatedQuery;
        const finalPopulate = populate || this.settings.populateOptions;

        return await ctx.call(`${ this.name }.find`, { query: finalQuery, populate: finalPopulate, sort });
      }
    },
  },

  /**
   * Methods
   */
  methods: {
    formatRegexString(str) {
      const arrStr = str?.toString()?.split("");
      if (!Array.isArray(arrStr)) return "";
      arrStr.forEach((char, index) => {
        switch (char) {
          case "(":
            arrStr[index] = "\\(";
            break;
          case ")":
            arrStr[index] = "\\)";
            break;
          case "\\":
            arrStr[index] = "\\\\";
            break;
          default:
            break;
        }
      });
      return arrStr.join("");
    },
    convertSearchFields(searchFields, query) {
      const fieldsArray = searchFields.split(",");
      for (let item of fieldsArray) {
        query[item] = new RegExp(this.formatRegexString(query[item]), "i");
      }
      return query;
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
