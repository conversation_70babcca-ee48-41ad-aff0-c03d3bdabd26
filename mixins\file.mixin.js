"use strict";

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('moleculer-db').MoleculerDB} MoleculerDB  Moleculer's DB Service Schema
 */

const fs = require("fs");
const path = require("path");
const uploadDir = path.join(__dirname, "storage");

const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffmpeg = require('fluent-ffmpeg');
ffmpeg.setFfmpegPath(ffmpegPath);

module.exports = {
  methods: {
    saveToLocalStorage(stream, filePath) {
      return new this.Promise((resolve, reject) => {
        //reject(new Error("Disk out of space"));
        const f = fs.createWriteStream(filePath);
        f.on("close", async () => {
          // File written successfully
          this.logger.info(`Uploaded file stored in '${ filePath }'`);
          resolve(filePath);
        });

        stream.on("error", (err) => {
          this.logger.info("File error received====================================1", err.message);
          reject(err);
          // Destroy the local file
          f.destroy(err);
        });

        f.on("error", (err) => {
          this.logger.info("File error received====================================2", err.message);
          // Remove the errored file.
          reject(err);
          fs.unlinkSync(filePath);
        });

        stream.pipe(f);
      });
    },
    createUniqueFileName(fileNameOrigin) {
      let fileName;
      if (fileNameOrigin) {
        let timeStamp = (new Date()).toISOString();
        timeStamp = timeStamp.replace(/:/g, '-');
        fileName = this.appendSuffix(fileNameOrigin, `_${ timeStamp }`);
      }
      return fileName;
    },
    appendSuffix(fileNameOrigin, suffix) {
      let fileName;
      if (fileNameOrigin) {
        let fileExtension = this.getFileExtension(fileNameOrigin);
        let name = path.parse(fileNameOrigin).name;
        fileName = fileExtension === '' ? `${ name }${ suffix }` : `${ name }${ suffix }.${ fileExtension }`;
      }
      return fileName;
    },
    appendFileName(fileNameOrigin, type = 'prefix', appendString) {
      if (!fileNameOrigin) return;

      const fileExtension = this.getFileExtension(fileNameOrigin);
      const name = path.parse(fileNameOrigin).name;

      const newName = type === 'prefix' ? `${ appendString }${ name }` : `${ name }${ appendString }`;
      return fileExtension === '' ? newName : `${ newName }.${ fileExtension }`;

    },
    getFileExtension(filename) {
      let ext = /^.+\.([^.]+)$/.exec(filename);
      return ext === null ? '' : ext[1];
    },
    getFilePath(fileName = '', filesDir = uploadDir) {
      if (!fileName) return null;
      return path.join(filesDir, fileName);
    },
    createFolderIfNotExist(folderPath) {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }
    },
    getDirPath(dirName = "", rootPath = '') {
      const dirPath = path.resolve(rootPath, dirName);
      this.createFolderIfNotExist(dirPath);
      return dirPath;
    },
    async writeStream(filePath) {
      const writeStream = fs.createWriteStream(filePath);
      return new Promise((resolve) => {
        writeStream.on("finish", () => {
          resolve();
        });
      });
    },
    clearFolder(folderPath) {
      if (fs.existsSync(folderPath)) {
        fs.readdirSync(folderPath).forEach((file) => {
          const curPath = `${ folderPath }/${ file }`;
          if (fs.lstatSync(curPath).isDirectory()) {
            this.clearFolder(curPath);
          } else { // delete file
            fs.unlinkSync(curPath);
          }
        });
        fs.rmdirSync(folderPath);
      }
    }
  },
};
