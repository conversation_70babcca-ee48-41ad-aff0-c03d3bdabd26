"use strict";

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('moleculer-db').MoleculerDB} MoleculerDB  Moleculer's DB Service Schema
 */
const { PERMISSION_ACCESS } = require("../constants/constant");
const showdown = require('showdown');
const Ajv = require("ajv");
const { convert } = require('html-to-text');
const axios = require('axios');

module.exports = {
  methods: {

   

  }
};
