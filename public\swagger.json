{"openapi": "3.0.0", "info": {"title": "Moleculer Base API", "description": "API documentation for Moleculer Base microservices", "version": "1.0.0", "contact": {"email": "<EMAIL>"}}, "servers": [{"url": "/api", "description": "API Gateway"}], "tags": [{"name": "Authentication", "description": "Authentication endpoints"}, {"name": "Users", "description": "User management endpoints"}], "paths": {"/users/login": {"post": {"tags": ["Authentication"], "summary": "Login with email and password", "description": "Authenticate a user with email and password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Account locked", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Create a new user account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "fullName", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "fullName": {"type": "string", "example": "<PERSON>"}, "password": {"type": "string", "format": "password", "example": "password123"}, "phone": {"type": "string", "example": "+**********"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "example": "male"}}}}}}, "responses": {"200": {"description": "Registration successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "user": {"$ref": "#/components/schemas/User"}}}}}}, "422": {"description": "Validation error or email already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/forgotPassword": {"post": {"tags": ["Authentication"], "summary": "Request password reset", "description": "Send a password reset link to the user's email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}}}}, "responses": {"200": {"description": "Password reset email sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Check your email for reset link"}}}}}}}}}, "/users/resetPassword": {"post": {"tags": ["Authentication"], "summary": "Reset password", "description": "Reset password using token from email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["token", "password"], "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "password": {"type": "string", "format": "password", "example": "newpassword123"}}}}}}, "responses": {"200": {"description": "Password reset successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Password reset successfully"}}}}}}, "400": {"description": "Invalid or expired token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/me": {"get": {"tags": ["Users"], "summary": "Get current user profile", "description": "Get the profile of the currently authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/profile": {"get": {"tags": ["Users"], "summary": "Get user profile", "description": "Get the profile of the currently authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user profile", "description": "Update the profile of the currently authenticated user", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fullName": {"type": "string", "example": "<PERSON>"}, "phone": {"type": "string", "example": "+**********"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "example": "male"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}}}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/changePassword": {"post": {"tags": ["Users"], "summary": "Change password", "description": "Change the password of the currently authenticated user", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["oldPassword", "newPassword"], "properties": {"oldPassword": {"type": "string", "format": "password", "example": "oldpassword123"}, "newPassword": {"type": "string", "format": "password", "example": "newpassword123"}}}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "user": {"$ref": "#/components/schemas/User"}}}}}}, "400": {"description": "Old password is incorrect", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/logout": {"post": {"tags": ["Authentication"], "summary": "Logout", "description": "Lo<PERSON>ut the currently authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout successful"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"_id": {"type": "string", "example": "5f7d8a9b9d3e2c1a3b5c7d9e"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "fullName": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "active": {"type": "boolean", "example": true}, "phone": {"type": "string", "example": "+**********"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "example": "male"}, "isSystemAdmin": {"type": "boolean", "example": false}, "lastLogin": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000Z"}, "role": {"type": "string", "example": "user"}}}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error message"}, "code": {"type": "integer", "example": 400}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}