# ARI Handler Service - AI-Powered Call Center

## Tổng quan

Service `ariHandler` là một hệ thống xử lý cuộc gọi tự động sử dụng AI, tích hợp với Asterisk REST Interface (ARI) để:

- Nhận cuộc gọi tự động
- Ghi âm giọng nói người gọi
- Chuyển đổi speech-to-text bằng OpenAI Whisper
- Tạo phản hồi thông minh bằng AI
- Chuyển đổi text-to-speech và phát lại cho người gọi
- Quản lý hội thoại đa lượt

## Tính năng chính

### 🎯 Xử lý cuộc gọi tự động
- Trả lời cuộc gọi tự động
- Chào hỏi bằng AI voice
- Quản lý trạng thái cuộc gọi (state machine)

### 🎙️ Ghi âm và xử lý
- Ghi âm giọng nói người gọi
- Hỗ trợ định dạng WAV
- Timeout và silence detection
- Cleanup tự động

### 🤖 Tích hợp AI
- Speech-to-text với OpenAI Whisper
- AI response generation với LangChain
- Text-to-speech với OpenAI TTS
- Conversation context management

### 📊 Monitoring và Statistics
- Theo dõi cuộc gọi active
- Thống kê cuộc gọi thành công/thất bại
- Logging chi tiết
- Health monitoring

## Cấu hình

### Environment Variables

```bash
# ARI Connection
ARI_URL=http://localhost:8088
ARI_USERNAME=cskh-1
ARI_PASSWORD=abc123@1
ARI_APP=ghvn

# OpenAI API Key (cho Whisper và TTS)
OPENAI_API_KEY=your_openai_api_key_here
```

### Service Settings

```javascript
settings: {
  // Thời gian tối đa cho một cuộc gọi (5 phút)
  maxCallDuration: 5 * 60 * 1000,
  
  // Timeout cho recording (30 giây)
  recordingTimeout: 30 * 1000,
  
  // Số lượt hội thoại tối đa
  maxConversationTurns: 10,
  
  // Tin nhắn chào mặc định
  defaultGreeting: "Xin chào! Tôi là trợ lý AI của GHVN...",
  
  // Thư mục lưu recordings
  recordingsPath: path.join(__dirname, "recordings")
}
```

## Cách sử dụng

### 1. Cấu hình Asterisk

Trong file `extensions.conf`:

```
[default]
exten => _X.,1,NoOp(Incoming call)
 same => n,Stasis(ghvn)
 same => n,Hangup()
```

### 2. Khởi động service

Service sẽ tự động khởi động khi Moleculer broker start:

```bash
npm start
```

### 3. Kiểm tra trạng thái

```bash
# Xem thống kê cuộc gọi
curl http://localhost:3000/api/ariHandler/stats

# Kết thúc cuộc gọi cụ thể
curl -X POST http://localhost:3000/api/ariHandler/end-call \
  -H "Content-Type: application/json" \
  -d '{"channelId": "channel_id_here"}'
```

## Flow xử lý cuộc gọi

```
📞 Cuộc gọi đến
    ↓
✅ Trả lời tự động
    ↓
🎵 Phát lời chào AI
    ↓
👂 Lắng nghe người gọi
    ↓
🎙️ Ghi âm (tối đa 30s)
    ↓
📝 Chuyển đổi speech-to-text (Whisper)
    ↓
🤖 Tạo phản hồi AI (LangChain)
    ↓
🔊 Chuyển đổi text-to-speech (TTS)
    ↓
📢 Phát phản hồi cho người gọi
    ↓
🔄 Lặp lại (tối đa 10 lượt)
    ↓
👋 Kết thúc cuộc gọi
```

## DTMF Commands

Người gọi có thể sử dụng các phím:

- `#`: Kết thúc recording hiện tại và xử lý
- `*`: Hủy operation hiện tại
- `0`: Chuyển đến nhân viên (future feature)
- `9`: Kết thúc cuộc gọi

## API Endpoints

### GET /api/ariHandler/stats
Lấy thống kê cuộc gọi

**Response:**
```json
{
  "activeCalls": 2,
  "totalCalls": 150,
  "successfulCalls": 145,
  "failedCalls": 5
}
```

### POST /api/ariHandler/end-call
Kết thúc cuộc gọi cụ thể

**Request:**
```json
{
  "channelId": "PJSIP/1001-00000001"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Call ended successfully"
}
```

## Logging

Service sử dụng structured logging với các level:

- `INFO`: Thông tin cuộc gọi, trạng thái
- `WARN`: Cảnh báo, lỗi không nghiêm trọng
- `ERROR`: Lỗi nghiêm trọng, exception

Ví dụ log:
```
[INFO] 📞 Incoming call from 0901234567 {"channelId": "PJSIP/1001-00000001"}
[INFO] 📝 User said: "Tôi muốn hỏi về sản phẩm" {"channelId": "PJSIP/1001-00000001"}
[INFO] 🤖 AI response: "Tôi có thể giúp bạn..." {"channelId": "PJSIP/1001-00000001"}
```

## Troubleshooting

### Lỗi kết nối ARI
```
❌ Failed to connect to ARI: Error: connect ECONNREFUSED
```
**Giải pháp:** Kiểm tra Asterisk đang chạy và ARI được enable

### Lỗi Whisper service
```
❌ Transcription failed: File size is greater than 20MB
```
**Giải pháp:** Giảm thời gian recording hoặc tối ưu audio format

### Lỗi TTS service
```
❌ TTS failed: OpenAI API error
```
**Giải pháp:** Kiểm tra OPENAI_API_KEY và quota

## Dependencies

Service phụ thuộc vào:

- `whisper`: Speech-to-text service
- `tts`: Text-to-speech service  
- `langchain`: AI response generation
- `ari-client`: Asterisk REST Interface client

## Performance

### Khuyến nghị
- Tối đa 50 cuộc gọi đồng thời
- Recording timeout: 30 giây
- Conversation turns: 10 lượt
- Call duration: 5 phút

### Monitoring
- Active calls được monitor mỗi 30 giây
- Cleanup tự động cho stale calls
- Memory cleanup cho recordings

## Security

- Recordings được cleanup tự động sau xử lý
- Không lưu trữ conversation history lâu dài
- API key được bảo vệ qua environment variables
