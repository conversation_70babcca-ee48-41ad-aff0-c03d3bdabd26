const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { BUSINESS, USER } = require('../../constants/dbCollections');

const { Schema } = mongoose;

// Define business categories
const businessCategories = {
  LOST_PACKAGE: 'lost_package',
  rush_delivery: 'rush_delivery',
  COD_EDIT: 'cod_edit',
  DELAYED_AT_BRANCH: 'delayed_at_branch',
  GH1P_EDIT: 'gh1p_edit',
  RETURN_REQUEST: 'return_request',
  COMPLAINT: 'complaint',
  INQUIRY: 'inquiry',
  OTHER: 'other'
};

// Define business status
const businessStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DRAFT: 'draft'
};

const businessSchema = new Schema({
  category: {
    type: String,
    enum: Object.values(businessCategories),
    required: true,
    index: true
  },
  keywords: [{
    type: String,
    trim: true,
    required: true
  }],
  examples: [{
    type: String,
    trim: true
  }],
  response: {
    type: String,
    required: true,
    trim: true
  },
  embedding: [{
    type: Number
  }],
  status: {
    type: String,
    enum: Object.values(businessStatus),
    default: businessStatus.ACTIVE,
    index: true
  },
  priority: {
    type: Number,
    default: 0,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: USER,
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: USER
  },
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  }
}, {
  timestamps: true,
  versionKey: false
});

// Add compound indexes for better query performance
businessSchema.index({ category: 1, status: 1 });
businessSchema.index({ keywords: 1 });
businessSchema.index({ tags: 1 });
businessSchema.index({ priority: -1, createdAt: -1 });
businessSchema.index({ createdBy: 1 });

businessSchema.plugin(mongoosePaginate);

// Export enums for use in service
businessSchema.statics.businessCategories = businessCategories;
businessSchema.statics.businessStatus = businessStatus;

module.exports = mongoose.model(BUSINESS, businessSchema, BUSINESS);
