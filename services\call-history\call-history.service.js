"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const CallHistory = require("./call-history.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");

module.exports = {
  name: "call-history",
  mixins: [DbMongoose(CallHistory), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/call-history",

    /** Public fields */
    fields: [
      "_id", "callId", "sipSessionId", "callerNumber", "receiverNumber", 
      "callType", "callStatus", "startTime", "endTime", "duration",
      "audioUrl", "audioFileId", "audioSize", "aiHandled", "aiTranscript", 
      "aiResponse", "agentId", "customerInfo", "metadata", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      callId: { type: "string" },
      sipSessionId: { type: "string" },
      callerNumber: { type: "string" },
      receiverNumber: { type: "string" },
      callType: { type: "enum", values: ["incoming", "outgoing"] },
      callStatus: { type: "enum", values: ["connecting", "completed", "missed", "rejected", "ai_answered"] },
      startTime: { type: "date" },
      endTime: { type: "date", optional: true },
      duration: { type: "number", optional: true },
      audioUrl: { type: "string", optional: true },
      audioFileId: { type: "string", optional: true },
      audioSize: { type: "number", optional: true },
      aiHandled: { type: "boolean", optional: true },
      aiTranscript: { type: "string", optional: true },
      aiResponse: { type: "string", optional: true },
      agentId: { type: "string", optional: true },
      customerInfo: { type: "object", optional: true },
      metadata: { type: "object", optional: true }
    },
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Bắt đầu ghi lại cuộc gọi
     */
    startCall: {
      rest: "POST /start",
      auth: "required",
      params: {
        sipSessionId: "string",
        callerNumber: "string", 
        receiverNumber: "string",
        callType: { type: "enum", values: ["incoming", "outgoing"] },
        customerInfo: { type: "object", optional: true }
      },
      async handler(ctx) {
        try {
          const { sipSessionId, callerNumber, receiverNumber, callType, customerInfo } = ctx.params;
          
          // Tạo callId duy nhất
          const callId = this.generateCallId();
          
          // Tạo record cuộc gọi mới
          const callRecord = await this.adapter.insert({
            callId,
            sipSessionId,
            callerNumber,
            receiverNumber,
            callType,
            callStatus: "connecting",
            startTime: new Date(),
            aiHandled: false,
            customerInfo: customerInfo || {},
            metadata: {},
            createdAt: new Date()
          });

          this.logger.info(`Call started: ${callId}`, { sipSessionId, callerNumber, receiverNumber });

          return {
            success: true,
            callId,
            record: await this.transformDocuments(ctx, {}, callRecord)
          };
        } catch (error) {
          this.logger.error("Failed to start call:", error);
          throw new MoleculerClientError(
            i18next.t("call_start_failed") || "Failed to start call recording",
            500,
            "CALL_START_ERROR"
          );
        }
      }
    },

    /**
     * Kết thúc cuộc gọi và upload audio
     */
    endCall: {
      rest: "POST /end",
      auth: "required", 
      params: {
        callId: "string",
        callStatus: { type: "enum", values: ["completed", "rejected", "missed"] },
        audioBlob: { type: "any", optional: true }, // Base64 hoặc Buffer
        metadata: { type: "object", optional: true }
      },
      async handler(ctx) {
        try {
          const { callId, audioBlob, callStatus, metadata } = ctx.params;

          // Tìm call record
          const callRecord = await this.adapter.findOne({ callId });
          if (!callRecord) {
            throw new MoleculerClientError(
              i18next.t("call_not_found") || "Call not found",
              404
            );
          }

          const endTime = new Date();
          const duration = Math.floor((endTime - callRecord.startTime) / 1000); // Tính duration bằng giây

          let updateData = {
            endTime,
            callStatus,
            duration,
            updatedAt: new Date()
          };

          // Upload audio file nếu có
          if (audioBlob) {
            try {
              const audioUpload = await ctx.call("files.upload", audioBlob, {
                meta: {
                  ...ctx.meta,
                  filename: `call_${callId}_${Date.now()}.wav`,
                  mimetype: "audio/wav"
                }
              });

              updateData.audioUrl = `/files/content/${audioUpload._id}`;
              updateData.audioFileId = audioUpload._id;
              updateData.audioSize = audioUpload.size;

              this.logger.info(`Audio uploaded for call: ${callId}`, { fileId: audioUpload._id });
            } catch (uploadError) {
              this.logger.error("Failed to upload audio:", uploadError);
              // Không throw error, chỉ log để không làm fail toàn bộ quá trình
            }
          }

          // Cập nhật metadata nếu có
          if (metadata) {
            updateData.metadata = { ...callRecord.metadata, ...metadata };
          }

          // Cập nhật call record
          const updated = await this.adapter.updateById(callRecord._id, updateData);

          this.logger.info(`Call ended: ${callId}`, { duration, callStatus });

          return {
            success: true,
            record: await this.transformDocuments(ctx, {}, updated)
          };
        } catch (error) {
          this.logger.error("Failed to end call:", error);
          throw new MoleculerClientError(
            error.message || "Failed to end call",
            error.code || 500,
            "CALL_END_ERROR"
          );
        }
      }
    },

    /**
     * Lấy lịch sử cuộc gọi với phân trang và filter
     */
    getCallHistory: {
      rest: "GET /history",
      auth: "required",
      params: {
        page: { type: "number", default: 1, min: 1 },
        limit: { type: "number", default: 20, min: 1, max: 100 },
        agentId: { type: "string", optional: true },
        dateFrom: { type: "string", optional: true }, // ISO date string
        dateTo: { type: "string", optional: true },
        callType: { type: "enum", values: ["incoming", "outgoing"], optional: true },
        callStatus: { type: "string", optional: true },
        aiHandled: { type: "boolean", optional: true },
        search: { type: "string", optional: true } // Tìm kiếm theo số điện thoại
      },
      async handler(ctx) {
        try {
          const { 
            page, limit, agentId, dateFrom, dateTo, 
            callType, callStatus, aiHandled, search 
          } = ctx.params;

          // Xây dựng query
          const query = { isDeleted: { $ne: true } };
          
          if (agentId) query.agentId = agentId;
          if (callType) query.callType = callType;
          if (callStatus) query.callStatus = callStatus;
          if (aiHandled !== undefined) query.aiHandled = aiHandled;
          
          // Filter theo thời gian
          if (dateFrom || dateTo) {
            query.startTime = {};
            if (dateFrom) query.startTime.$gte = new Date(dateFrom);
            if (dateTo) query.startTime.$lte = new Date(dateTo);
          }

          // Tìm kiếm theo số điện thoại
          if (search) {
            query.$or = [
              { callerNumber: { $regex: search, $options: 'i' } },
              { receiverNumber: { $regex: search, $options: 'i' } },
              { 'customerInfo.phone': { $regex: search, $options: 'i' } },
              { 'customerInfo.name': { $regex: search, $options: 'i' } }
            ];
          }

          // Thực hiện query với pagination
          const options = {
            page,
            limit,
            sort: { startTime: -1 },
            populate: [
              { path: 'agentId', select: 'firstName lastName email' },
              { path: 'audioFileId', select: 'name displayName size mimetype' }
            ]
          };

          const results = await this.adapter.model.paginate(query, options);

          return {
            success: true,
            data: results.docs,
            pagination: {
              total: results.totalDocs,
              page: results.page,
              limit: results.limit,
              totalPages: results.totalPages,
              hasNextPage: results.hasNextPage,
              hasPrevPage: results.hasPrevPage
            }
          };
        } catch (error) {
          this.logger.error("Failed to get call history:", error);
          throw new MoleculerClientError(
            i18next.t("get_call_history_failed") || "Failed to get call history",
            500,
            "GET_CALL_HISTORY_ERROR"
          );
        }
      }
    },

    /**
     * Cập nhật thông tin AI xử lý cuộc gọi
     */
    updateAiHandling: {
      params: {
        callId: "string",
        aiHandled: "boolean",
        aiTranscript: { type: "string", optional: true },
        aiResponse: { type: "string", optional: true }
      },
      async handler(ctx) {
        try {
          const { callId, aiHandled, aiTranscript, aiResponse } = ctx.params;

          const updateData = {
            aiHandled,
            updatedAt: new Date()
          };

          if (aiTranscript) updateData.aiTranscript = aiTranscript;
          if (aiResponse) updateData.aiResponse = aiResponse;

          const updated = await this.adapter.updateOne(
            { callId }, 
            updateData
          );

          if (!updated) {
            throw new MoleculerClientError(
              i18next.t("call_not_found") || "Call not found",
              404
            );
          }

          this.logger.info(`AI handling updated for call: ${callId}`, { aiHandled });

          return {
            success: true,
            updated: true
          };
        } catch (error) {
          this.logger.error("Failed to update AI handling:", error);
          throw new MoleculerClientError(
            error.message || "Failed to update AI handling",
            error.code || 500,
            "UPDATE_AI_HANDLING_ERROR"
          );
        }
      }
    }
  },

  /**
   * Events
   */
  events: {
    "user.removed": {
      async handler(ctx) {
        const userId = ctx.params.id;
        // Khi user bị xóa, cập nhật agentId thành null
        await this.adapter.updateMany(
          { agentId: userId }, 
          { agentId: null, updatedAt: new Date() }
        );
        this.logger.info(`Updated call history for removed user: ${userId}`);
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Tạo callId duy nhất
     */
    generateCallId() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      return `call_${timestamp}_${random}`;
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("Call History service created");
  },

  /**
   * Service started lifecycle event handler
   */
  started() {
    this.logger.info("Call History service started");
  }
};
