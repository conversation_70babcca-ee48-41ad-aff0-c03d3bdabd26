const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { CONVERSATIONS, USER, MESSAGES} = require('../../constants/dbCollections');

const { Schema } = mongoose;

// Define customer types
const customerTypes = {
  BUYER: 'buyer',
  SHOP: 'shop'
};

// Define conversation status
const conversationStatus = {
  OPEN: 'open',
  CLOSED: 'closed'
};

const conversationSchema = new Schema({
  customerId: {
    type: Schema.Types.ObjectId,
    ref: USER,
    required: true,
    index: true
  },
  customerType: {
    type: String,
    enum: Object.values(customerTypes),
    required: true,
    index: true
  },
  supportId: {
    type: Schema.Types.ObjectId,
    ref: USER,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(conversationStatus),
    default: conversationStatus.OPEN,
    index: true
  },
  closedAt: {
    type: Date
  },
  tags: [{
    type: String,
    trim: true
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: USER
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: USER
  },
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  }
}, {
  timestamps: true,
  versionKey: false
});

// Add compound indexes for better query performance
conversationSchema.index({ customerId: 1, status: 1 });
conversationSchema.index({ supportId: 1, status: 1 });
conversationSchema.index({ customerType: 1, status: 1 });
conversationSchema.index({ tags: 1 });

conversationSchema.plugin(mongoosePaginate);

// Export customer types and status for use in service
conversationSchema.statics.customerTypes = customerTypes;
conversationSchema.statics.conversationStatus = conversationStatus;

module.exports = mongoose.model(CONVERSATIONS, conversationSchema, CONVERSATIONS);
