const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { FILES, USER } = require('../../constants/dbCollections');

const { Schema } = mongoose;
const filesSchema = new Schema({
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: USER
  },
  name: {
    type: String,
    required: true
  },
  displayName: {
    type: String,
    required: true
  },
  fileType: {
    type: String,
    required: true
  },
  mimetype: {
    type: String,
    required: true
  },
  size: {
    type: String,
    required: true
  },
  storageType: {
    type: String,
    default: 'local_storage'
  },
  storageLocation: {
    type: String,
    required: true
  },
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  },
  used: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

filesSchema.plugin(mongoosePaginate);

module.exports = mongoose.model(FILES, filesSchema, FILES);
