"use strict";

/**
 * Business Logic Handler
 * Contains all business logic handling methods for different categories
 */

/**
 * <PERSON><PERSON> lost package business logic with compensation
 * @param {Object} ctx - Moleculer context
 * @param {Object} params - Parameters containing question and userType
 * @returns {Array} Array of recommendations
 */
async function handleLostPackageLogic(ctx, {question, userType}) {
  // Extract order code from question if possible
  const orderCodeMatch = question.match(/GHN\d+/i);
  const orderCode = orderCodeMatch ? orderCodeMatch[0] : null;

  const recommendations = [];

  if (userType === "shop") {
    recommendations.push("🔍 Kiểm tra mã vận đơn và xác nhận trạng thái thất lạc");
    recommendations.push("📋 Thu thập thông tin: khai giá, cước phí, có bảo hiểm không");
    
    if (orderCode) {
      recommendations.push(`📦 Đơn hàng: ${orderCode} - Kiểm tra chi tiết trong hệ thống`);
    }
    
    recommendations.push("💰 Chính sách đền bù:");
    recommendations.push("   • Có bảo hiểm: Đền theo giá trị khai báo");
    recommendations.push("   • Không bảo hiểm: Tối đa 4 lần cước phí vận chuyển");
    recommendations.push("📄 Yêu cầu chứng từ: ảnh chuyển khoản, tin nhắn đặt hàng");
    recommendations.push("🏦 Chuẩn bị thông tin tài khoản ngân hàng để nhận đền bù");
    recommendations.push("⏱️ Thời gian xử lý: 3-5 ngày làm việc");
    recommendations.push("📞 Thông báo cho khách hàng về tình trạng và quy trình đền bù");
  } else {
    // CSKH logic
    recommendations.push("✅ Xác nhận đơn hàng bị thất lạc trong hệ thống");
    recommendations.push("📊 Kiểm tra thông tin: khai giá, cước phí, bảo hiểm");
    recommendations.push("💵 Tính toán mức đền bù theo chính sách");
    recommendations.push("📋 Hướng dẫn shop cung cấp chứng từ bổ sung");
    recommendations.push("📝 Tạo hồ sơ xử lý đền bù và chuyển bộ phận chuyên trách");
    recommendations.push("📱 Cập nhật tiến độ qua chat box");
  }

  return recommendations;
}

/**
 * Handle COD modification and address change business logic
 * @param {Object} ctx - Moleculer context
 * @param {Object} params - Parameters containing question and userType
 * @returns {Array} Array of recommendations
 */
async function handleCODModificationLogic(ctx, {question, userType}) {
  // Extract order code from question if possible
  const orderCodeMatch = question.match(/GHN\d+/i);
  const orderCode = orderCodeMatch ? orderCodeMatch[0] : null;

  // Extract COD amount if mentioned
  const codMatch = question.match(/(\d+)k|(\d+)\.?(\d+)?k|(\d+)\.?(\d+)?\s*(nghìn|ngàn|k)/i);
  const codAmount = codMatch ? codMatch[0] : null;

  // Check if this is address modification
  const addressKeywords = ['sửa địa chỉ', 'đổi địa chỉ', 'thay đổi địa chỉ', 'địa chỉ mới'];
  const isAddressChange = addressKeywords.some(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  const recommendations = [];

  if (userType === "shop") {
    if (orderCode) {
      recommendations.push(`📦 Đơn hàng: ${orderCode} - Yêu cầu thay đổi thông tin`);
    }

    if (codAmount) {
      recommendations.push(`💰 COD mới: ${codAmount}`);
    }

    if (isAddressChange) {
      recommendations.push("📍 THAY ĐỔI ĐỊA CHỈ:");
      recommendations.push("🔍 Kiểm tra trạng thái đơn hàng hiện tại");
      recommendations.push("📋 Xác minh địa chỉ mới với khách hàng");
      recommendations.push("📞 Liên hệ bưu cục nếu hàng đã xuất kho");
      recommendations.push("⚠️ Lưu ý: Có thể phát sinh phí bổ sung");
    } else {
      recommendations.push("💰 THAY ĐỔI COD:");
      recommendations.push("🔍 Xác minh thông tin đơn hàng hiện tại");
      recommendations.push("✅ Kiểm tra khả năng thay đổi COD");
      recommendations.push("📝 Cập nhật thông tin trong hệ thống");
      recommendations.push("✅ Xác nhận với khách hàng về số tiền COD mới");
    }
    
    recommendations.push("📞 Thông báo cho đơn vị vận chuyển về thay đổi");
    recommendations.push("📱 Theo dõi và xác nhận thay đổi thành công");
  } else {
    // CSKH logic
    if (orderCode) {
      recommendations.push(`🔍 Tra cứu đơn ${orderCode} trong hệ thống`);
    }

    if (isAddressChange) {
      recommendations.push("📍 XỬ LÝ THAY ĐỔI ĐỊA CHỈ:");
      recommendations.push("📊 Kiểm tra trạng thái và vị trí đơn hàng");
      recommendations.push("🏢 Xác định bưu cục đang xử lý");
      recommendations.push("📝 Cập nhật địa chỉ mới trong hệ thống");
      recommendations.push("📞 Thông báo bưu cục về thay đổi");
      recommendations.push("💰 Tính toán phí bổ sung nếu có");
    } else {
      recommendations.push("💰 XỬ LÝ THAY ĐỔI COD:");
      recommendations.push("📊 Kiểm tra thông tin COD hiện tại");
      recommendations.push("✅ Xác nhận khả năng thay đổi");
      recommendations.push("📝 Cập nhật COD mới trong hệ thống");
      recommendations.push("🔔 Thông báo shipper về COD mới");
    }
    
    recommendations.push("✅ Xác nhận thay đổi thành công với shop");
    recommendations.push("📱 Cập nhật trạng thái xử lý");
  }

  return recommendations;
}

/**
 * Handle delivery reminder and urgent delivery business logic
 * @param {Object} ctx - Moleculer context
 * @param {Object} params - Parameters containing question and userType
 * @returns {Array} Array of recommendations
 */
async function handleDeliveryReminderLogic(ctx, {question, userType}) {
  // Extract order code from question if possible
  const orderCodeMatch = question.match(/GHN\d+/i);
  const orderCode = orderCodeMatch ? orderCodeMatch[0] : null;
  
  // Check if this is urgent delivery request
  const urgentKeywords = ['giục giao', 'dí giao', 'giao gấp', 'cần gấp', 'khách hối'];
  const isUrgent = urgentKeywords.some(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  const recommendations = [];

  if (userType === "shop") {
    if (orderCode) {
      recommendations.push(`📦 Đơn hàng: ${orderCode} - Kiểm tra trạng thái hiện tại`);
    }
    
    if (isUrgent) {
      recommendations.push("🚨 YÊU CẦU GIAO GẤP:");
      recommendations.push("⏰ Xác định thời gian cụ thể khách cần nhận");
      recommendations.push("📍 Xác nhận địa chỉ giao hàng chính xác");
      recommendations.push("📞 Liên hệ ngay bưu cục phụ trách để ưu tiên");
      recommendations.push("🔔 Yêu cầu shipper gọi trước khi giao");
      recommendations.push("📱 Theo dõi sát và cập nhật cho khách");
    } else {
      recommendations.push("📋 Kiểm tra trạng thái giao hàng hiện tại");
      recommendations.push("📞 Liên hệ đơn vị vận chuyển để xác nhận");
      recommendations.push("⏱️ Thông báo thời gian giao hàng dự kiến");
      recommendations.push("📨 Gửi thông báo nhắc nhở cho khách hàng");
      recommendations.push("🔄 Cập nhật trạng thái đơn hàng");
    }
  } else {
    // CSKH logic
    if (orderCode) {
      recommendations.push(`🔍 Tra cứu đơn ${orderCode} trong hệ thống`);
    }
    
    if (isUrgent) {
      recommendations.push("🚨 XỬ LÝ YÊU CẦU GIAO GẤP:");
      recommendations.push("📊 Kiểm tra vị trí hiện tại của đơn hàng");
      recommendations.push("🏢 Xác định bưu cục đang xử lý");
      recommendations.push("📞 Liên hệ ngay bưu cục để ưu tiên giao");
      recommendations.push("⏰ Ghi nhận thời gian yêu cầu của khách");
      recommendations.push("📝 Tạo ghi chú ưu tiên trên đơn hàng");
      recommendations.push("📱 Theo dõi và cập nhật tiến độ cho shop");
    } else {
      recommendations.push("📋 Kiểm tra lịch sử và trạng thái đơn hàng");
      recommendations.push("🚚 Xác nhận với đội giao hàng");
      recommendations.push("📞 Liên hệ khách hàng xác nhận thông tin");
      recommendations.push("⏱️ Cung cấp thời gian giao hàng dự kiến");
    }
  }

  return recommendations;
}

/**
 * Handle delayed at branch business logic
 * @param {Object} ctx - Moleculer context
 * @param {Object} params - Parameters containing question and userType
 * @returns {Array} Array of recommendations
 */
async function handleDelayedAtBranchLogic(ctx, {question, userType}) {
  // Extract order code from question if possible
  const orderCodeMatch = question.match(/GHN\d+/i);
  const orderCode = orderCodeMatch ? orderCodeMatch[0] : null;

  const recommendations = [];

  if (userType === "shop") {
    if (orderCode) {
      recommendations.push(`📦 Đơn hàng: ${orderCode} - Kiểm tra thời gian tồn tại bưu cục`);
    }
    
    recommendations.push("🏢 Xác định bưu cục đang giữ hàng");
    recommendations.push("📅 Kiểm tra số ngày hàng tồn tại bưu cục");
    recommendations.push("📞 Liên hệ trực tiếp bưu cục để hỏi lý do chậm trễ");
    recommendations.push("⚡ Yêu cầu ưu tiên giao ngay trong ngày");
    recommendations.push("📱 Theo dõi sát sao và yêu cầu cập nhật liên tục");
    recommendations.push("📞 Thông báo cho khách hàng về tình trạng và thời gian dự kiến");
  } else {
    // CSKH logic
    if (orderCode) {
      recommendations.push(`🔍 Tra cứu đơn ${orderCode} - Kiểm tra lịch sử tại bưu cục`);
    }
    
    recommendations.push("📊 Kiểm tra thời gian hàng tồn tại bưu cục");
    recommendations.push("🏢 Xác định bưu cục và lý do chậm trễ");
    recommendations.push("📞 Liên hệ ngay bưu cục để yêu cầu ưu tiên giao");
    recommendations.push("📝 Ghi nhận yêu cầu giao gấp vào hệ thống");
    recommendations.push("⏰ Cam kết thời gian giao cụ thể với shop");
    recommendations.push("📱 Theo dõi và cập nhật tiến độ thường xuyên");
  }

  return recommendations;
}

/**
 * Handle GH1P (Giao 1 Phần) modification business logic
 * @param {Object} ctx - Moleculer context
 * @param {Object} params - Parameters containing question and userType
 * @returns {Array} Array of recommendations
 */
async function handleGH1PModificationLogic(ctx, {question, userType}) {
  // Extract order code from question if possible
  const orderCodeMatch = question.match(/GHN\d+/i);
  const orderCode = orderCodeMatch ? orderCodeMatch[0] : null;

  // Extract COD amount if mentioned
  const codMatch = question.match(/(\d+)k|(\d+)\.?(\d+)?k|(\d+)\.?(\d+)?\s*(nghìn|ngàn|k)/i);
  const codAmount = codMatch ? codMatch[0] : null;

  const recommendations = [];

  if (userType === "shop") {
    if (orderCode) {
      recommendations.push(`📦 Đơn hàng: ${orderCode} - Yêu cầu sửa GH1P`);
    }
    
    if (codAmount) {
      recommendations.push(`💰 COD mới: ${codAmount}`);
    }
    
    recommendations.push("⚠️ LƯU Ý: GH1P có thể gây rủi ro gian lận");
    recommendations.push("💡 KHUYẾN NGHỊ: Sửa COD GTB TT thay vì GH1P");
    recommendations.push("📋 Quy trình GTB TT:");
    recommendations.push("   • Hạ COD GTB TT xuống giá trị mong muốn");
    recommendations.push("   • Giữ nguyên COD đơn hàng gốc");
    recommendations.push("   • Ghi chú rõ số lượng giao/thu hồi");
    recommendations.push("🔒 Bảo vệ shop khỏi rủi ro shipper gian lận");
    recommendations.push("✅ Xác nhận phương thức xử lý với CSKH");
  } else {
    // CSKH logic
    if (orderCode) {
      recommendations.push(`🔍 Kiểm tra đơn ${orderCode} - Yêu cầu GH1P`);
    }
    
    recommendations.push("💡 ĐỀ XUẤT GIẢI PHÁP AN TOÀN:");
    recommendations.push("🔄 Kiến nghị sử dụng GTB TT thay vì GH1P");
    recommendations.push("📝 Giải thích lợi ích GTB TT cho shop:");
    recommendations.push("   • Hạ COD GTB TT xuống mức cần thiết");
    recommendations.push("   • Giữ nguyên COD đơn hàng để tránh gian lận");
    recommendations.push("   • Ghi chú chi tiết số lượng giao/thu hồi");
    recommendations.push("❓ Hỏi ý kiến shop về phương án GTB TT");
    recommendations.push("✅ Nếu shop đồng ý: Thực hiện GTB TT");
    recommendations.push("⚠️ Nếu shop yêu cầu GH1P: Thực hiện theo yêu cầu");
    recommendations.push("📱 Cập nhật kết quả cho shop");
  }

  return recommendations;
}

module.exports = {
  handleLostPackageLogic,
  handleCODModificationLogic,
  handleDeliveryReminderLogic,
  handleDelayedAtBranchLogic,
  handleGH1PModificationLogic
};
