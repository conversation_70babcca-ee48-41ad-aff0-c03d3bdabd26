"use strict";

const fs = require("fs");
const {OpenAI} = require("openai");
const {ChatOpenAI, OpenAIEmbeddings} = require("@langchain/openai");
const {ChatPromptTemplate} = require("@langchain/core/prompts");
const {ChatGoogleGenerativeAI} = require("@langchain/google-genai");
const {CacheBackedEmbeddings} = require("langchain/embeddings/cache_backed");
const {InMemoryStore} = require("langchain/storage/in_memory");
const {similarity} = require("ml-distance");

const model = "text-embedding-3-small"
const configuration = {
  apiKey:
    process.env.OPENAI_API_KEY ||
    "********************************************************************************************************************************************************************",
};
const underlyingEmbeddings = new OpenAIEmbeddings({apiKey: configuration.apiKey, model});
const inMemoryStore = new InMemoryStore();
const cacheBackedEmbeddings = CacheBackedEmbeddings.fromBytesStore(underlyingEmbeddings, inMemoryStore, {
  namespace: underlyingEmbeddings.modelName,
});


/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "langchain",

  /**
   * Settings
   */
  settings: {}, hooks: {}, /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    transcriptAudio: {
      timeout: 5 * 60 * 1000, // visibility: "protected",
      rest: {
        method: "GET",
        path: "/transcript",
      },
      async handler(ctx) {
        const audioPath = ctx.params.audioPath;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 9.96) {
            return {
              error: "File size is greater than 10MB, try smaller video",
            };
          }
          const {apiKey} = ctx.meta;
          const openai = new OpenAI({apiKey: apiKey || configuration.apiKey});
          // New
          return await openai.audio.transcriptions.create({
            model: "whisper-1", file: fs.createReadStream(audioPath),
          });
        } catch (err) {
          return err;
        }
      },
    },

    chatCompletion: {
      timeout: 5 * 60 * 1000, rest: {
        method: "POST", path: "/chatCompletion",
      }, // visibility: "protected",
      async handler(ctx) {
        try {
          let {
            messages,
            model = "gpt-3.5-turbo-1106" || "gpt-4-1106-preview" || "gpt-3.5-turbo-0613",
            modelInterface = 'ChatOpenAI',
            schema,
            responseFormat,
            responseId,
            temperature,
            apiKey,
          } = ctx.params;

          const chatModel = this.getModel(ctx, modelInterface, {
            apiKey: apiKey || configuration.apiKey, model, temperature, maxTokens: 4000
          });
          const promptMessages = this.transformMessages(modelInterface, messages);

          const prompt = ChatPromptTemplate.fromMessages(promptMessages);
          const completion = {};
          // const steamResponse = await chain.stream()
          if (responseFormat === 'json_object') {
            // const chain = prompt.pipe(chatModel.bindTools([formatContentTool]))
            const chain = prompt.pipe(chatModel.withStructuredOutput(schema));
            const result = await chain.invoke('');
            completion.usage = {
              completion_tokens: 0, prompt_tokens: 0, total_tokens: 0,
            };
            completion.content = result;
          } else {
            const chain = prompt.pipe(chatModel);
            const result = await chain.invoke('');
            completion.usage = {
              completion_tokens: result.input_tokens,
              prompt_tokens: result.output_tokens,
              total_tokens: result.total_tokens,
            };
            completion.content = result.content;
          }
          return completion.content;

        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },

    getEmbedding: {
      timeout: 5 * 60 * 1000, async handler(ctx) {
        try {
          let {
            textContent,
          } = ctx.params;

          let time = Date.now();
          const result = await cacheBackedEmbeddings.embedDocuments([textContent]);

          // console.log(`Creation time: ${Date.now() - time}ms`);
          // console.log("textContent", textContent, result[0].length, result[0][0] );
          return result[0];
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
    getRelatedScore: {
      timeout: 5 * 60 * 1000, // visibility: "protected",
      async handler(ctx) {
        try {
          let {
            vectors,
          } = ctx.params;

          // console.log("vectors", vectors);
          const score = similarity.cosine(vectors[0], vectors[1])
          // console.log("similarity", vectors[0].length, vectors[1].length, score);
          // console.log("similarity", vectors[0][0], vectors[1][0], score);

          return score
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },

    /**
     * Text to Speech action using OpenAI TTS
     *
     * @param {string} text - Text to convert to speech
     * @param {string} voice - Voice to use (alloy, echo, fable, onyx, nova, shimmer)
     * @param {string} model - TTS model to use (tts-1, tts-1-hd)
     * @param {number} speed - Speech speed (0.25 to 4.0)
     * @param {string} apiKey - OpenAI API key
     * @returns {Object} Audio file information
     */
    textToSpeech: {
      timeout: 6 * 60 * 1000,
      rest: {
        method: "POST",
        path: "/textToSpeech",
      },
      params: {
        text: { type: "string", min: 1, max: 4096 },
        voice: { type: "string", optional: true, default: "alloy", enum: ["alloy", "echo", "fable", "onyx", "nova", "shimmer"] },
        model: { type: "string", optional: true, default: "tts-1", enum: ["tts-1", "tts-1-hd"] },
        speed: { type: "number", optional: true, default: 1.0, min: 0.25, max: 4.0 },
        apiKey: { type: "string", optional: true }
      },
      async handler(ctx) {
        try {
          const {
            text,
            voice = "alloy",
            model = "tts-1",
            speed = 1.0,
            apiKey = ctx.meta.apiKey || apiKey
          } = ctx.params;

          console.log("TTS Request:", { model, voice, speed, textLength: text.length });

          // Initialize OpenAI client
          const openai = new OpenAI({ apiKey: apiKey || configuration.apiKey });

          // Create speech using OpenAI TTS API
          const audio = await openai.audio.speech.create({
            model: model,
            voice: voice,
            speed: speed,
            input: text,
            response_format: "mp3"
          });

          console.log("TTS audio generated successfully");

          // Convert audio to buffer
          const buffer = Buffer.from(await audio.arrayBuffer());

          // Create file record using the files service
          const file = await ctx.call("files.createFromAudioBuffer", { buffer });

          return {
            success: true,
            file: file,
            metadata: {
              model,
              voice,
              speed,
              textLength: text.length,
              audioSize: buffer.length
            }
          };

        } catch (err) {
          console.error("TTS Error:", err);
          return {
            success: false,
            error: err.message || "Text to speech conversion failed",
            details: err
          };
        }
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    getModel(ctx, modelInterface, modelOptions) {
      if (modelInterface === "ChatOpenAI") {
        return new ChatOpenAI(modelOptions);
      }
      if (modelInterface === "GoogleGenerativeAI") {
        return new ChatGoogleGenerativeAI(modelOptions);
      }
      return new ChatOpenAI(modelOptions);
    },
    transformMessages(modelInterface, messages) {
      const promptMessages = messages.map(mes => [mes.role, mes.content]);
      if (modelInterface === "ChatOpenAI") {
        return promptMessages;
      }
      if (modelInterface === "GoogleGenerativeAI") {
        return this.transformToGemini(promptMessages);
      }
      return promptMessages;
    },
    transformToGemini(messagesChatgpt) {
      let messagesGemini = [];
      let systemPrompt = [];

      messagesChatgpt.forEach(message => {
        const [role, content] = message;
        if (role === 'system') {
          systemPrompt.push(message[1]);
        } else if (role === 'user') {
          messagesGemini.push([role, content]);
        } else if (role === 'assistant') {
          messagesGemini.push([role, content]);
        }
      });

      if (systemPrompt) {
        messagesGemini[0][1] = `*${systemPrompt.join('\n')}*\n` + messagesGemini[0][1];
      }

      return messagesGemini;
    },

    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
