"use strict";

/**
 * Context Extractor Utility
 * Extracts business keywords and context from user questions
 */

/**
 * Extract business keywords and context from question
 * @param {string} question - User question to analyze
 * @returns {Object} Extracted business context
 */
function extractBusinessContext(question) {
  const context = {
    orderCode: null,
    codAmount: null,
    urgentKeywords: [],
    lostKeywords: [],
    gh1pKeywords: [],
    addressKeywords: [],
    branchKeywords: []
  };

  // Extract order code
  const orderCodeMatch = question.match(/GHN\d+/i);
  if (orderCodeMatch) {
    context.orderCode = orderCodeMatch[0];
  }

  // Extract COD amount
  const codMatch = question.match(/(\d+)k|(\d+)\.?(\d+)?k|(\d+)\.?(\d+)?\s*(nghìn|ngàn|k)/i);
  if (codMatch) {
    context.codAmount = codMatch[0];
  }

  // Check for urgent delivery keywords
  const urgentKeywords = ['giục giao', 'dí giao', 'giao gấp', 'cần gấp', 'kh<PERSON>ch hối', 'ưu tiên'];
  context.urgentKeywords = urgentKeywords.filter(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  // Check for lost package keywords
  const lostKeywords = ['mất', 'thất lạc', 'tráo', 'tráo hàng', 'bị mất', 'không tìm thấy'];
  context.lostKeywords = lostKeywords.filter(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  // Check for GH1P keywords
  const gh1pKeywords = ['gh1p', 'g1p', 'giao 1 phần', 'giao một phần', 'thu hồi'];
  context.gh1pKeywords = gh1pKeywords.filter(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  // Check for address modification keywords
  const addressKeywords = ['sửa địa chỉ', 'đổi địa chỉ', 'thay đổi địa chỉ', 'địa chỉ mới'];
  context.addressKeywords = addressKeywords.filter(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  // Check for branch/delay keywords
  const branchKeywords = ['bưu cục', 'tồn kho', 'chậm giao', 'không giao', 'đã lâu'];
  context.branchKeywords = branchKeywords.filter(keyword => 
    question.toLowerCase().includes(keyword.toLowerCase())
  );

  return context;
}

module.exports = {
  extractBusinessContext
};
