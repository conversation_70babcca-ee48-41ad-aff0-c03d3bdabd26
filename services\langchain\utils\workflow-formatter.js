"use strict";

/**
 * Workflow Formatter Utility
 * Formats workflow responses for user display
 */

/**
 * Format workflow response for user display
 * @param {Object} workflowResult - Workflow execution result
 * @returns {string} Formatted response string
 */
function formatWorkflowResponse(workflowResult) {
  if (!workflowResult || !workflowResult.recommendations) {
    return "Xin lỗi, có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại.";
  }

  let response = workflowResult.recommendations.join('\n');
  
  // Add workflow progress indicator
  if (workflowResult.state && workflowResult.state.currentStep) {
    const stepMap = {
      'verification': '1/4',
      'documentation': '2/4', 
      'approval': '3/4',
      'payment': '4/4',
      'risk_assessment': '1/4',
      'recommendation': '2/4',
      'user_choice': '3/4',
      'execution': '4/4',
      'priority_assessment': '1/4',
      'branch_contact': '2/4',
      'tracking': '3/4',
      'updates': '4/4',
      'validation': '1/3',
      'system_update': '2/3',
      'confirmation': '3/3',
      'investigation': '1/3',
      'escalation': '2/3',
      'resolution': '3/3',
      'complete': 'Hoàn tất'
    };
    
    const progress = stepMap[workflowResult.state.currentStep] || '';
    if (progress && progress !== 'Hoàn tất') {
      response += `\n\n📊 Tiến độ: ${progress}`;
    }
  }

  // Add completion message
  if (workflowResult.isComplete) {
    response += '\n\n✅ Quy trình đã hoàn tất. Cảm ơn bạn đã sử dụng dịch vụ!';
  }

  return response;
}

module.exports = {
  formatWorkflowResponse
};
