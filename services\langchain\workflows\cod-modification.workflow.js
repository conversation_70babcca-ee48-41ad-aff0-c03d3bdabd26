"use strict";

const {StateGraph, END, START, Annotation} = require("@langchain/langgraph");

// Define the state schema
const WorkflowState = Annotation.Root({
  category: Annotation(),
  question: Annotation(),
  userType: Annotation(),
  businessContext: Annotation(),
  currentStep: Annotation(),
  recommendations: Annotation(),
  workflowData: Annotation(),
  orderCode: Annotation(),
  modificationType: Annotation(),
  newValue: Annotation()
});

/**
 * Create COD/Address Modification Workflow
 * States: validation → system_update → confirmation
 * @returns {StateGraph} LangGraph workflow for COD/address modifications
 */
function createCODModificationWorkflow() {
  const workflow = new StateGraph(WorkflowState);

  // Step 1: Validation
  workflow.addNode("validation", async (state) => {
    const recommendations = [];
    const orderCode = state.businessContext?.orderCode;
    const codAmount = state.businessContext?.codAmount;
    const isAddressChange = state.businessContext?.addressKeywords?.length > 0;
    
    if (state.userType === "shop") {
      recommendations.push("✅ BƯỚC 1: XÁC MINH THÔNG TIN");
      if (orderCode) {
        recommendations.push(`📦 Đơn hàng: ${orderCode}`);
      }
      if (codAmount) {
        recommendations.push(`💰 COD mới: ${codAmount}`);
      }
      
      if (isAddressChange) {
        recommendations.push("📍 THAY ĐỔI ĐỊA CHỈ:");
        recommendations.push("   • Cung cấp địa chỉ mới chính xác");
        recommendations.push("   • Xác nhận với khách hàng");
        recommendations.push("   • Lưu ý có thể phát sinh phí bổ sung");
      } else {
        recommendations.push("💰 THAY ĐỔI COD:");
        recommendations.push("   • Xác nhận số tiền COD mới");
        recommendations.push("   • Đảm bảo khách hàng đồng ý");
      }
      
      recommendations.push("📞 CSKH sẽ kiểm tra và cập nhật");
    } else {
      recommendations.push("✅ BƯỚC 1: KIỂM TRA KHẢ NĂNG THAY ĐỔI");
      if (orderCode) {
        recommendations.push(`🔍 Tra cứu đơn ${orderCode}`);
      }
      recommendations.push("📊 Kiểm tra trạng thái đơn hàng");
      recommendations.push("🏢 Xác định bưu cục đang xử lý");
      
      if (isAddressChange) {
        recommendations.push("📍 Xử lý thay đổi địa chỉ:");
        recommendations.push("   • Kiểm tra khả năng thay đổi");
        recommendations.push("   • Tính phí bổ sung nếu có");
      } else {
        recommendations.push("💰 Xử lý thay đổi COD:");
        recommendations.push("   • Xác nhận COD hiện tại");
        recommendations.push("   • Kiểm tra giới hạn thay đổi");
      }
    }

    return {
      ...state,
      currentStep: "system_update",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        validationComplete: true,
        modificationType: isAddressChange ? "address" : "cod",
        orderCode: orderCode
      }
    };
  });

  // Step 2: System Update
  workflow.addNode("system_update", async (state) => {
    const recommendations = [];
    const isAddressChange = state.workflowData?.modificationType === "address";
    
    if (state.userType === "shop") {
      recommendations.push("🔄 BƯỚC 2: CẬP NHẬT HỆ THỐNG");
      recommendations.push("⚡ CSKH đang thực hiện thay đổi");
      
      if (isAddressChange) {
        recommendations.push("📍 Cập nhật địa chỉ mới:");
        recommendations.push("   • Thay đổi trong hệ thống");
        recommendations.push("   • Thông báo bưu cục");
        recommendations.push("   • Tính phí bổ sung (nếu có)");
      } else {
        recommendations.push("💰 Cập nhật COD mới:");
        recommendations.push("   • Thay đổi số tiền COD");
        recommendations.push("   • Thông báo shipper");
        recommendations.push("   • Cập nhật ghi chú đơn hàng");
      }
      
      recommendations.push("⏳ Chờ xác nhận hoàn tất");
    } else {
      recommendations.push("🔄 BƯỚC 2: THỰC HIỆN CẬP NHẬT");
      
      if (isAddressChange) {
        recommendations.push("📍 Cập nhật địa chỉ giao hàng:");
        recommendations.push("   • Sửa địa chỉ trong hệ thống");
        recommendations.push("   • Thông báo bưu cục về thay đổi");
        recommendations.push("   • Tính toán phí bổ sung");
        recommendations.push("   • Cập nhật lộ trình giao hàng");
      } else {
        recommendations.push("💰 Cập nhật COD:");
        recommendations.push("   • Thay đổi số tiền COD");
        recommendations.push("   • Thông báo shipper về COD mới");
        recommendations.push("   • Cập nhật ghi chú đơn hàng");
        recommendations.push("   • Xác nhận với hệ thống thanh toán");
      }
    }

    return {
      ...state,
      currentStep: "confirmation",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        systemUpdateComplete: true
      }
    };
  });

  // Step 3: Confirmation
  workflow.addNode("confirmation", async (state) => {
    const recommendations = [];
    const isAddressChange = state.workflowData?.modificationType === "address";
    
    if (state.userType === "shop") {
      recommendations.push("✅ BƯỚC 3: XÁC NHẬN HOÀN TẤT");
      
      if (isAddressChange) {
        recommendations.push("📍 Địa chỉ đã được cập nhật thành công");
        recommendations.push("🏢 Bưu cục đã nhận thông báo thay đổi");
        recommendations.push("💰 Phí bổ sung (nếu có) sẽ được thông báo");
      } else {
        recommendations.push("💰 COD đã được cập nhật thành công");
        recommendations.push("🚚 Shipper đã nhận thông báo COD mới");
      }
      
      recommendations.push("📱 Kiểm tra lại thông tin trên app/website");
      recommendations.push("📞 Liên hệ CSKH nếu cần hỗ trợ thêm");
      recommendations.push("✅ Quy trình hoàn tất");
    } else {
      recommendations.push("✅ BƯỚC 3: XÁC NHẬN VỚI SHOP");
      
      if (isAddressChange) {
        recommendations.push("📍 Đã cập nhật địa chỉ thành công");
        recommendations.push("🏢 Đã thông báo bưu cục");
        recommendations.push("💰 Đã tính phí bổ sung (nếu có)");
      } else {
        recommendations.push("💰 Đã cập nhật COD thành công");
        recommendations.push("🚚 Đã thông báo shipper");
      }
      
      recommendations.push("📞 Xác nhận với shop về thay đổi");
      recommendations.push("📝 Cập nhật trạng thái hoàn tất");
      recommendations.push("✅ Đóng case");
    }

    return {
      ...state,
      currentStep: "complete",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        confirmationComplete: true,
        workflowComplete: true
      }
    };
  });

  // Define workflow edges
  workflow.addEdge(START, "validation");
  workflow.addEdge("validation", "system_update");
  workflow.addEdge("system_update", "confirmation");
  workflow.addEdge("confirmation", END);

  return workflow;
}

module.exports = {
  createCODModificationWorkflow
};
