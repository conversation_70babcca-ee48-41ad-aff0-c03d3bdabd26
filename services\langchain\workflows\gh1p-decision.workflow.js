"use strict";

const {StateGraph, END, START, Annotation} = require("@langchain/langgraph");

// Define the state schema
const WorkflowState = Annotation.Root({
  category: Annotation(),
  question: Annotation(),
  userType: Annotation(),
  businessContext: Annotation(),
  currentStep: Annotation(),
  recommendations: Annotation(),
  workflowData: Annotation(),
  orderCode: Annotation(),
  codAmount: Annotation(),
  riskLevel: Annotation(),
  userChoice: Annotation(),
  executionMethod: Annotation()
});

/**
 * Create GH1P vs GTB TT Decision Workflow
 * States: risk_assessment → recommendation → user_choice → execution
 * @returns {StateGraph} LangGraph workflow for GH1P decision process
 */
function createGH1PWorkflow() {
  const workflow = new StateGraph(WorkflowState);

  // Step 1: Risk Assessment
  workflow.addNode("risk_assessment", async (state) => {
    const recommendations = [];
    const orderCode = state.businessContext?.orderCode;
    const codAmount = state.businessContext?.codAmount;
    
    if (state.userType === "shop") {
      recommendations.push("⚠️ BƯỚC 1: ĐÁNH GIÁ RỦI RO GH1P");
      if (orderCode) {
        recommendations.push(`📦 Đơn hàng: ${orderCode}`);
      }
      if (codAmount) {
        recommendations.push(`💰 COD hiện tại: ${codAmount}`);
      }
      recommendations.push("🚨 RỦI RO GH1P:");
      recommendations.push("   • Shipper có thể gian lận COD");
      recommendations.push("   • Khó kiểm soát số lượng giao/thu hồi");
      recommendations.push("   • Rủi ro mất hàng hoặc tiền");
      recommendations.push("💡 GIẢI PHÁP AN TOÀN: GTB TT");
      recommendations.push("   • Bảo vệ shop khỏi gian lận");
      recommendations.push("   • Kiểm soát chặt chẽ hơn");
    } else {
      recommendations.push("⚠️ BƯỚC 1: PHÂN TÍCH YÊU CẦU GH1P");
      if (orderCode) {
        recommendations.push(`🔍 Đơn hàng: ${orderCode}`);
      }
      recommendations.push("📊 Đánh giá rủi ro:");
      recommendations.push("   • Mức độ phức tạp đơn hàng");
      recommendations.push("   • Lịch sử gian lận trong khu vực");
      recommendations.push("   • Giá trị COD và hàng hóa");
      recommendations.push("💡 Chuẩn bị khuyến nghị GTB TT");
    }

    return {
      ...state,
      currentStep: "recommendation",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        riskAssessmentComplete: true,
        orderCode: orderCode,
        codAmount: codAmount
      }
    };
  });

  // Step 2: Recommendation
  workflow.addNode("recommendation", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("💡 BƯỚC 2: KHUYẾN NGHỊ GTB TT");
      recommendations.push("🔄 QUY TRÌNH GTB TT AN TOÀN:");
      recommendations.push("   • Hạ COD GTB TT xuống mức cần thiết");
      recommendations.push("   • Giữ nguyên COD đơn hàng gốc");
      recommendations.push("   • Ghi chú chi tiết: 'Giao X sản phẩm, thu hồi Y sản phẩm'");
      recommendations.push("🔒 LỢI ÍCH GTB TT:");
      recommendations.push("   • Shipper không thể gian lận COD");
      recommendations.push("   • Shop được bảo vệ tối đa");
      recommendations.push("   • Quy trình minh bạch, rõ ràng");
      recommendations.push("❓ Shop có đồng ý sử dụng GTB TT không?");
    } else {
      recommendations.push("💡 BƯỚC 2: ĐỀ XUẤT GIẢI PHÁP");
      recommendations.push("🔄 KIẾN NGHỊ GTB TT CHO SHOP:");
      recommendations.push("📝 Giải thích lợi ích GTB TT:");
      recommendations.push("   • An toàn hơn GH1P");
      recommendations.push("   • Tránh rủi ro gian lận");
      recommendations.push("   • Kiểm soát tốt hơn");
      recommendations.push("❓ Hỏi ý kiến shop về phương án GTB TT");
      recommendations.push("📋 Chuẩn bị thực hiện theo lựa chọn của shop");
    }

    return {
      ...state,
      currentStep: "user_choice",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        recommendationGiven: true
      }
    };
  });

  // Step 3: User Choice
  workflow.addNode("user_choice", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("🤔 BƯỚC 3: LỰA CHỌN PHƯƠNG THỨC");
      recommendations.push("✅ OPTION 1: GTB TT (Khuyến nghị)");
      recommendations.push("   • An toàn, bảo mật cao");
      recommendations.push("   • Tránh rủi ro gian lận");
      recommendations.push("⚠️ OPTION 2: GH1P (Có rủi ro)");
      recommendations.push("   • Thực hiện theo yêu cầu");
      recommendations.push("   • Shop tự chịu rủi ro");
      recommendations.push("📞 Thông báo lựa chọn cho CSKH");
    } else {
      recommendations.push("🤔 BƯỚC 3: CHỜ QUYẾT ĐỊNH SHOP");
      recommendations.push("⏳ Đợi shop phản hồi về phương án:");
      recommendations.push("✅ Nếu shop chọn GTB TT:");
      recommendations.push("   • Thực hiện GTB TT ngay");
      recommendations.push("   • Ghi chú chi tiết trên đơn");
      recommendations.push("⚠️ Nếu shop yêu cầu GH1P:");
      recommendations.push("   • Thực hiện theo yêu cầu");
      recommendations.push("   • Ghi chú rủi ro đã thông báo");
      recommendations.push("📱 Cập nhật kết quả cho shop");
    }

    return {
      ...state,
      currentStep: "execution",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        awaitingUserChoice: true
      }
    };
  });

  // Step 4: Execution
  workflow.addNode("execution", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("⚡ BƯỚC 4: THỰC HIỆN THAY ĐỔI");
      recommendations.push("✅ CSKH sẽ thực hiện theo lựa chọn của shop");
      recommendations.push("📝 Theo dõi cập nhật từ CSKH");
      recommendations.push("📞 Liên hệ nếu cần hỗ trợ thêm");
      recommendations.push("✅ Quy trình hoàn tất");
    } else {
      recommendations.push("⚡ BƯỚC 4: THỰC HIỆN THAY ĐỔI");
      recommendations.push("🔄 Thực hiện phương án đã chọn:");
      recommendations.push("✅ GTB TT: Hạ COD GTB TT + ghi chú");
      recommendations.push("⚠️ GH1P: Thực hiện GH1P + ghi chú rủi ro");
      recommendations.push("📝 Cập nhật hệ thống");
      recommendations.push("📞 Xác nhận với shop");
      recommendations.push("✅ Hoàn tất xử lý");
    }

    return {
      ...state,
      currentStep: "complete",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        executionComplete: true,
        workflowComplete: true
      }
    };
  });

  // Define workflow edges
  workflow.addEdge(START, "risk_assessment");
  workflow.addEdge("risk_assessment", "recommendation");
  workflow.addEdge("recommendation", "user_choice");
  workflow.addEdge("user_choice", "execution");
  workflow.addEdge("execution", END);

  return workflow;
}

module.exports = {
  createGH1PWorkflow
};
