const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { MESSAGES, USER, CONVERSATIONS } = require('../../constants/dbCollections');

const { Schema } = mongoose;

// Define sender roles
const senderRoles = {
  BUYER: 'buyer',
  SHOP: 'shop',
  SUPPORT: 'support'
};

const messageSchema = new Schema({
  conversationId: {
    type: Schema.Types.ObjectId,
    ref: CONVERSATIONS,
    required: true,
    index: true
  },
  senderId: {
    type: Schema.Types.ObjectId,
    ref: USER,
    required: true,
    index: true
  },
  senderRole: {
    type: String,
    enum: Object.values(senderRoles),
    required: true,
    index: true
  },
  text: {
    type: String,
    required: true,
    trim: true
  },
  attachments: [{
    fileId: {
      type: Schema.Types.ObjectId,
      ref: 'Files'
    },
    fileName: {
      type: String,
      trim: true
    },
    fileType: {
      type: String,
      trim: true
    },
    fileSize: {
      type: Number
    },
    fileUrl: {
      type: String,
      trim: true
    }
  }],
  sentAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  read: {
    type: Boolean,
    default: false,
    index: true
  },
  readAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  }
}, {
  timestamps: true,
  versionKey: false
});

// Add compound indexes for better query performance
messageSchema.index({ conversationId: 1, sentAt: -1 });
messageSchema.index({ senderId: 1, sentAt: -1 });
messageSchema.index({ conversationId: 1, read: 1 });
messageSchema.index({ senderRole: 1, sentAt: -1 });

messageSchema.plugin(mongoosePaginate);

// Export sender roles for use in service
messageSchema.statics.senderRoles = senderRoles;

module.exports = mongoose.model(MESSAGES, messageSchema, MESSAGES);
