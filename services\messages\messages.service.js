"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const MESSAGES = require("./messages.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const mongoose = require("mongoose");

module.exports = {
  name: "messages",
  mixins: [DbMongoose(MESSAGES), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {

    /** Public fields */
    fields: [
      "_id", "conversationId", "senderId", "senderRole", "text",
      "attachments", "sentAt", "read", "readAt", "createdAt", "updatedAt"
    ],


    /** Populate options */
    populates: {
      senderId: {
        action: "users.get"
      },
      conversationId: {
        action: "conversations.get"
      }
    },
    populateOptions: ["senderId"],
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Create a new message
     */
    create: {
      rest: "POST /",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const entity = ctx.params;
        const senderId = user?._id || entity.senderId;
        // Check if conversation exists
        const conversation = await ctx.call("conversations.get", {id: entity.conversationId.toString()});
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }
        // Determine sender role based on user and conversation
        let senderRole = "support"; // default
        if (conversation.customerId._id.toString() === senderId.toString()) {
          senderRole = conversation.customerType;
        }

        // Create message
        const message = {
          conversationId: entity.conversationId.toString(),
          senderId,
          senderRole: senderRole,
          text: entity.text || entity.content,
          attachments: entity.attachments || [],
          sentAt: new Date(),
          read: false
        };

        const result = await this.adapter.insert(message);

        // Update conversation's updatedAt
        await ctx.call("conversations.update", {
          id: entity.conversationId.toString(),
          updatedBy: senderId
        });

        // Emit event for message creation
        await this.broker.emit('message.created', {
          messages: result,
          conversationId: entity.conversationId.toString(),
          senderId,
          senderRole: senderRole,
          text: entity.text || entity.content
        });

        return await this.transformDocuments(ctx, {populate: ["senderId"]}, result);
      }
    },

    /**
     * Get messages by conversation
     */
    getByConversation: {
      rest: "GET /:conversationId/messages",
      auth: "required",
      async handler(ctx) {
        const {conversationId} = ctx.params;

        // Check if conversation exists
        const conversation = await ctx.call("conversations.get", {id: conversationId.toString()});
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Convert string to ObjectId for proper querying
        const objectId = new mongoose.Types.ObjectId(conversationId);

        // Get messages with pagination
        const query = {
          conversationId: objectId,
          isDeleted: false
        };
        return this.adapter.find({
          query,
          populate: ["senderId"],
          sort: ["-sentAt"]
        });
      }
    },

    /**
     * Mark message as read
     */
    markAsRead: {
      rest: "PUT /:id/read",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {id} = ctx.params;

        // Check if message exists
        const message = await this.adapter.findById(id);
        if (!message) {
          throw new MoleculerClientError(i18next.t("Message not found"), 404);
        }

        // Only allow marking as read if user is not the sender
        if (message.senderId.toString() === user._id.toString()) {
          throw new MoleculerClientError(i18next.t("Cannot mark own message as read"), 400);
        }

        // Mark as read
        const result = await this.adapter.updateById(id, {
          $set: {
            read: true,
            readAt: new Date()
          }
        });

        // Emit event for message read
        this.broker.emit('message.read', {
          messageId: id,
          readBy: user._id,
          conversationId: message.conversationId
        });

        return this.transformDocuments(ctx, {populate: ["senderId"]}, result);
      }
    },

    /**
     * Mark all messages in conversation as read
     */
    markConversationAsRead: {
      rest: "PUT /conversation/:conversationId/read",
      auth: "required",
      params: {
        conversationId: "string"
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {conversationId} = ctx.params;

        // Check if conversation exists
        const conversation = await ctx.call("conversations.get", {id: conversationId.toString()});
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Convert string to ObjectId
        const objectId = new mongoose.Types.ObjectId(conversationId);

        // Mark all unread messages as read (except user's own messages)
        const result = await this.adapter.updateMany(
          {
            conversationId: objectId,
            senderId: {$ne: user._id},
            read: false,
            isDeleted: {$ne: true}
          },
          {
            $set: {
              read: true,
              readAt: new Date()
            }
          }
        );

        // Emit event for conversation read
        this.broker.emit('conversation.messages.read', {
          conversationId: conversationId,
          readBy: user._id,
          messagesCount: result.modifiedCount
        });

        return {
          success: true,
          messagesMarkedAsRead: result.modifiedCount
        };
      }
    },

    /**
     * Get unread messages count for user
     */
    getUnreadCount: {
      rest: "GET /unread/count",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;

        // Get all conversations where user is involved
        const conversations = await ctx.call("conversations.find", {
          query: {
            $or: [
              {customerId: user._id},
              {supportId: user._id}
            ],
            isDeleted: {$ne: true}
          }
        });

        const conversationIds = conversations.map(conv => new mongoose.Types.ObjectId(conv._id));

        // Count unread messages in these conversations (excluding user's own messages)
        const unreadCount = await this.adapter.count({
          conversationId: {$in: conversationIds},
          senderId: {$ne: user._id},
          read: false,
          isDeleted: {$ne: true}
        });

        return {unreadCount};
      }
    }
  }
};
