"use strict";

const fs = require("fs");
const {OpenAI} = require("openai");
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;

const FileMixin = require("../../../mixins/file.mixin");
const configuration = {
  apiKey:
    process.env.OPENAI_API_KEY ||
    "********************************************************************************************************************************************************************",
};

const path = require("path");
const storageDir = path.join(__dirname, "./storage");
const asteriskSoundsDir = process.env.ASTERISK_SOUNDS_DIR || "/var/lib/asterisk/sounds/voicebot";

// Set FFmpeg path
ffmpeg.setFfmpegPath(ffmpegPath);

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "tts",
  mixins: [FileMixin],
  /**
   * Settings
   */
  settings: {},
  hooks: {
    before: {}
  },
  /**
   * Dependencies
   */
  dependencies: ["ssh"],

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    textToSpeech: {
      timeout: 6 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/textToSpeech",
      },
      async handler(ctx) {
        const {text, voice = "alloy", model = "tts-1", speed = 1, forAsterisk = true} = ctx.params;

        try {
          this.logger.info(`🎵 Generating TTS audio for text: "${text.substring(0, 50)}..."`);

          const openai = new OpenAI({apiKey: configuration.apiKey});

          // Generate audio using OpenAI TTS
          const audio = await openai.audio.speech.create({
            model: model,
            voice: voice || "alloy",
            speed,
            input: text,
            response_format: "mp3"
          });

          const buffer = Buffer.from(await audio.arrayBuffer());
          this.logger.info(`🎵 TTS audio generated, size: ${buffer.length} bytes`);

          if (forAsterisk) {
            // Create temporary MP3 file
            const tempMp3Path = path.join(storageDir, `temp_${Date.now()}.mp3`);
            await fs.promises.writeFile(tempMp3Path, buffer);

            // Generate unique WAV filename for Asterisk
            const wavFilename = this.generateUniqueAudioFilename('tts', 'wav');
            const asteriskWavPath = this.getAsteriskSoundPath(wavFilename);

            try {
              // Convert MP3 to Asterisk-compatible WAV
              await this.convertToAsteriskWav(tempMp3Path, asteriskWavPath);

              // Verify the WAV file was created successfully
              if (!fs.existsSync(asteriskWavPath)) {
                throw new Error(`WAV file was not created: ${asteriskWavPath}`);
              }

              const wavStats = fs.statSync(asteriskWavPath);
              if (wavStats.size === 0) {
                throw new Error(`WAV file is empty: ${asteriskWavPath}`);
              }

              // Clean up temporary MP3 file
              fs.unlinkSync(tempMp3Path);

              this.logger.info(`🎵 Audio converted for Asterisk: ${asteriskWavPath}`, {
                size: wavStats.size,
                soundName: path.parse(wavFilename).name
              });

              // Copy file to remote Asterisk server via SSH
              let sshCopyResult = null;
              try {
                this.logger.info(`🚀 Copying file to remote Asterisk server...`);
                sshCopyResult = await ctx.call("ssh.copyFileToAsterisk", {
                  localFilePath: asteriskWavPath,
                  remoteFileName: wavFilename,
                  remoteSubDir: "voicebot" // Copy to /var/lib/asterisk/sounds/voicebot/
                });

                if (sshCopyResult.success) {
                  this.logger.info(`✅ File successfully copied to remote server`, {
                    localFile: asteriskWavPath,
                    remoteFile: sshCopyResult.remoteFilePath,
                    transferTime: sshCopyResult.transferTime
                  });
                } else {
                  this.logger.error(`❌ SSH copy failed: ${sshCopyResult.error}`);
                  // Continue with local file - don't fail the entire operation
                }
              } catch (sshError) {
                this.logger.error(`❌ SSH copy error:`, sshError);
                // Continue with local file - don't fail the entire operation
              }

              // Create file record for the WAV file
              const wavBuffer = fs.readFileSync(asteriskWavPath);

              let file = null;
              try {
                file = await ctx.call("files.createFromAudioBuffer", {
                  buffer: wavBuffer,
                  folder: 'asterisk-audio',
                  filename: wavFilename,
                  mimetype: 'audio/wav'
                });
              } catch (filesError) {
                this.logger.warn(`⚠️ Files service not available: ${filesError.message}`);
                // Create a mock file object for compatibility
                file = {
                  _id: path.parse(wavFilename).name,
                  name: wavFilename,
                  size: wavBuffer.length,
                  mimetype: 'audio/wav'
                };
              }

              return {
                success: true,
                file: file,
                asteriskPath: asteriskWavPath,
                soundName: path.parse(wavFilename).name, // Without extension for Asterisk
                filePath: asteriskWavPath,
                buffer: wavBuffer,
                // SSH copy information
                sshCopy: sshCopyResult ? {
                  success: sshCopyResult.success,
                  remoteFilePath: sshCopyResult.remoteFilePath,
                  transferTime: sshCopyResult.transferTime,
                  error: sshCopyResult.error
                } : null
              };

            } catch (conversionError) {
              // Clean up temp file on error
              if (fs.existsSync(tempMp3Path)) {
                fs.unlinkSync(tempMp3Path);
              }
              throw conversionError;
            }

          } else {
            // Original behavior for non-Asterisk use
            const dirPath = this.getDirPath('audio', storageDir);
            const filePath = this.getFilePath(`audio_${Date.now()}.mp3`, dirPath);
            await fs.promises.writeFile(filePath, buffer);
            const file = await ctx.call("files.createFromAudioBuffer", {buffer});

            return {
              success: true,
              file: file,
              filePath: filePath,
              buffer: buffer
            };
          }

        } catch (err) {
          this.logger.error(`❌ TTS Error: ${err.message}`, err);
          return {
            success: false,
            error: err.message || "Text to speech conversion failed",
            details: err
          };
        }
      },
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    /**
     * Convert MP3 audio to WAV format compatible with Asterisk
     * @param {string} inputPath - Path to input MP3 file
     * @param {string} outputPath - Path for output WAV file
     * @returns {Promise<string>} - Path to converted WAV file
     */
    async convertToAsteriskWav(inputPath, outputPath) {
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .audioCodec('pcm_s16le')  // 16-bit PCM
          .audioFrequency(8000)     // 8kHz sample rate for telephony
          .audioChannels(1)         // Mono
          .format('wav')
          .on('end', () => {
            this.logger.info(`Audio converted successfully: ${outputPath}`);
            resolve(outputPath);
          })
          .on('error', (err) => {
            this.logger.error(`Audio conversion failed: ${err.message}`);
            reject(err);
          })
          .save(outputPath);
      });
    },

    /**
     * Create Asterisk-compatible audio file path
     * @param {string} filename - Base filename
     * @returns {string} - Full path in Asterisk sounds directory
     */
    getAsteriskSoundPath(filename) {
      // Ensure the custom sounds directory exists
      if (!fs.existsSync(asteriskSoundsDir)) {
        this.logger.info(`📁 Creating Asterisk sounds directory: ${asteriskSoundsDir}`);
        fs.mkdirSync(asteriskSoundsDir, { recursive: true });

        // Try to set permissions (may fail on Windows)
        try {
          fs.chmodSync(asteriskSoundsDir, 0o755);
        } catch (permError) {
          this.logger.warn(`⚠️ Could not set directory permissions: ${permError.message}`);
        }
      }

      const fullPath = path.join(asteriskSoundsDir, filename);
      this.logger.info(`📁 Asterisk sound path: ${fullPath}`);
      return fullPath;
    },

    /**
     * Generate unique filename for audio files
     * @param {string} prefix - Filename prefix
     * @param {string} extension - File extension
     * @returns {string} - Unique filename
     */
    generateUniqueAudioFilename(prefix = 'tts', extension = 'wav') {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      return `${prefix}_${timestamp}_${random}.${extension}`;
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    // Create storage directories
    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, {recursive: true});
    }

    // Create Asterisk sounds directory
    if (!fs.existsSync(asteriskSoundsDir)) {
      fs.mkdirSync(asteriskSoundsDir, {recursive: true});
      this.logger.info(`📁 Created Asterisk sounds directory: ${asteriskSoundsDir}`);
    }
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
