const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const {SHOPS} = require('../../constants/dbCollections');

const {Schema} = mongoose;
const shopSchema = new Schema({
  name: {
    type: String,
    required: true
  },
  taxCode: {type: String},
  email: {type: String},
  phone: {type: String},
  address: {type: String},
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  },
}, {
  timestamps: true,
  versionKey: false,
});

shopSchema.plugin(mongoosePaginate);

module.exports = mongoose.model(SHOPS, shopSchema, SHOPS);
