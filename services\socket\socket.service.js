const {Server} = require("socket.io");

module.exports = {
  name: "socket",
  settings: {
    port: 3001,
  },
  dependencies: [],


  async started() {

    // Khởi tạo Socket.IO server
    this.io = new Server(this.settings.port, {
      transports: ["websocket"],
      path: "/socket",
    });

    // Phân biệt kết nối dựa trên namespace/path
    this.io.of("call-center").on("connection", (socket) => {
      this.broker.emit("call-center.socket.connected", socket);
    });

    this.logger.info("Socket.IO server is running...");

  },

  stopped() {
    if (this.io) {
      console.log("Shutting down WebSocket server...");
      this.io.close();
    }
  },
};
