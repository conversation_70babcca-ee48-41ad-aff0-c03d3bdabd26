"use strict";

const { NodeSSH } = require('node-ssh');
const fs = require('fs');
const path = require('path');

/**
 * SSH Service for copying files to remote Asterisk server
 * Handles secure file transfer to Asterisk sounds directory
 */
module.exports = {
  name: "ssh",

  /**
   * Service settings
   */
  settings: {
    // SSH connection settings
    host: process.env.SSH_HOST || "***************",
    username: process.env.SSH_USERNAME || "root", 
    password: process.env.SSH_PASSWORD || "Ey5WrsNDHynyEb4uawGO6hxWzIvOslTH",
    port: process.env.SSH_PORT || 22,
    
    // Remote paths
    remoteSoundsDir: process.env.REMOTE_SOUNDS_DIR || "/var/lib/asterisk/sounds",
    
    // Connection settings
    connectionTimeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 2000, // 2 seconds
  },

  /**
   * Service dependencies
   */
  dependencies: [],

  /**
   * Service actions
   */
  actions: {
    /**
     * Copy file to remote Asterisk server
     */
    copyFileToAsterisk: {
      params: {
        localFilePath: "string",
        remoteFileName: { type: "string", optional: true },
        remoteSubDir: { type: "string", optional: true, default: "" }
      },
      async handler(ctx) {
        const { localFilePath, remoteFileName, remoteSubDir } = ctx.params;
        
        try {
          this.logger.info(`🚀 Starting SSH copy operation`, {
            localFile: localFilePath,
            remoteFile: remoteFileName,
            remoteSubDir: remoteSubDir
          });

          // Validate local file exists
          if (!fs.existsSync(localFilePath)) {
            throw new Error(`Local file not found: ${localFilePath}`);
          }

          // Determine remote file name
          const finalRemoteFileName = remoteFileName || path.basename(localFilePath);
          
          // Build remote path
          const remoteDir = remoteSubDir 
            ? path.posix.join(this.settings.remoteSoundsDir, remoteSubDir)
            : this.settings.remoteSoundsDir;
          const remoteFilePath = path.posix.join(remoteDir, finalRemoteFileName);

          // Copy file with retry mechanism
          const result = await this.copyFileWithRetry(localFilePath, remoteFilePath, remoteDir);

          this.logger.info(`✅ SSH copy completed successfully`, {
            localFile: localFilePath,
            remoteFile: remoteFilePath,
            fileSize: result.fileSize
          });

          return {
            success: true,
            localFilePath: localFilePath,
            remoteFilePath: remoteFilePath,
            remoteFileName: finalRemoteFileName,
            fileSize: result.fileSize,
            transferTime: result.transferTime
          };

        } catch (error) {
          this.logger.error(`❌ SSH copy failed:`, error, {
            localFile: localFilePath,
            remoteFile: remoteFileName
          });

          return {
            success: false,
            error: error.message,
            localFilePath: localFilePath
          };
        }
      }
    },

    /**
     * Test SSH connection
     */
    testConnection: {
      async handler(ctx) {
        try {
          this.logger.info(`🔍 Testing SSH connection to ${this.settings.host}`);
          
          const ssh = new NodeSSH();
          await ssh.connect({
            host: this.settings.host,
            username: this.settings.username,
            password: this.settings.password,
            port: this.settings.port,
            readyTimeout: this.settings.connectionTimeout
          });

          // Test remote directory access
          const result = await ssh.execCommand(`ls -la ${this.settings.remoteSoundsDir}`);
          
          await ssh.dispose();

          this.logger.info(`✅ SSH connection test successful`);
          
          return {
            success: true,
            host: this.settings.host,
            remoteSoundsDir: this.settings.remoteSoundsDir,
            directoryExists: result.code === 0,
            directoryListing: result.stdout
          };

        } catch (error) {
          this.logger.error(`❌ SSH connection test failed:`, error);
          
          return {
            success: false,
            error: error.message,
            host: this.settings.host
          };
        }
      }
    },

    /**
     * Create remote directory if not exists
     */
    ensureRemoteDirectory: {
      params: {
        remoteDir: "string"
      },
      async handler(ctx) {
        const { remoteDir } = ctx.params;
        
        try {
          const ssh = new NodeSSH();
          await ssh.connect({
            host: this.settings.host,
            username: this.settings.username,
            password: this.settings.password,
            port: this.settings.port,
            readyTimeout: this.settings.connectionTimeout
          });

          // Create directory with proper permissions
          const createDirCommand = `mkdir -p ${remoteDir} && chmod 755 ${remoteDir}`;
          const result = await ssh.execCommand(createDirCommand);
          
          await ssh.dispose();

          if (result.code !== 0) {
            throw new Error(`Failed to create directory: ${result.stderr}`);
          }

          this.logger.info(`📁 Remote directory ensured: ${remoteDir}`);
          
          return {
            success: true,
            remoteDir: remoteDir,
            created: true
          };

        } catch (error) {
          this.logger.error(`❌ Failed to ensure remote directory:`, error);
          
          return {
            success: false,
            error: error.message,
            remoteDir: remoteDir
          };
        }
      }
    }
  },

  /**
   * Service methods
   */
  methods: {
    /**
     * Copy file with retry mechanism
     */
    async copyFileWithRetry(localFilePath, remoteFilePath, remoteDir) {
      let lastError = null;
      const startTime = Date.now();

      for (let attempt = 1; attempt <= this.settings.retryAttempts; attempt++) {
        try {
          this.logger.info(`🔄 SSH copy attempt ${attempt}/${this.settings.retryAttempts}`, {
            localFile: localFilePath,
            remoteFile: remoteFilePath
          });

          const result = await this.performSSHCopy(localFilePath, remoteFilePath, remoteDir);
          const transferTime = Date.now() - startTime;
          
          return {
            ...result,
            transferTime: transferTime,
            attempts: attempt
          };

        } catch (error) {
          lastError = error;
          this.logger.warn(`⚠️ SSH copy attempt ${attempt} failed:`, error.message);

          if (attempt < this.settings.retryAttempts) {
            this.logger.info(`⏳ Retrying in ${this.settings.retryDelay}ms...`);
            await new Promise(resolve => setTimeout(resolve, this.settings.retryDelay));
          }
        }
      }

      throw lastError || new Error("SSH copy failed after all retry attempts");
    },

    /**
     * Perform actual SSH copy operation
     */
    async performSSHCopy(localFilePath, remoteFilePath, remoteDir) {
      const ssh = new NodeSSH();
      
      try {
        // Connect to SSH
        await ssh.connect({
          host: this.settings.host,
          username: this.settings.username,
          password: this.settings.password,
          port: this.settings.port,
          readyTimeout: this.settings.connectionTimeout
        });

        // Ensure remote directory exists
        const createDirCommand = `mkdir -p ${remoteDir} && chmod 755 ${remoteDir}`;
        await ssh.execCommand(createDirCommand);

        // Get file size for verification
        const stats = fs.statSync(localFilePath);
        const fileSize = stats.size;

        // Copy file
        await ssh.putFile(localFilePath, remoteFilePath);

        // Verify file was copied successfully
        const verifyCommand = `ls -la ${remoteFilePath}`;
        const verifyResult = await ssh.execCommand(verifyCommand);
        
        if (verifyResult.code !== 0) {
          throw new Error(`File verification failed: ${verifyResult.stderr}`);
        }

        // Set proper permissions for Asterisk
        const chmodCommand = `chmod 644 ${remoteFilePath}`;
        await ssh.execCommand(chmodCommand);

        this.logger.info(`📁 File copied and permissions set`, {
          remoteFile: remoteFilePath,
          fileSize: fileSize
        });

        return {
          fileSize: fileSize,
          verified: true
        };

      } finally {
        await ssh.dispose();
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("🔐 SSH service created");
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    this.logger.info("🚀 SSH service started");
    
    // Test connection on startup
    try {
      const testResult = await this.actions.testConnection();
      if (testResult.success) {
        this.logger.info("✅ SSH service ready - connection verified");
      } else {
        this.logger.warn("⚠️ SSH service started but connection test failed:", testResult.error);
      }
    } catch (error) {
      this.logger.warn("⚠️ SSH service started but initial connection test failed:", error.message);
    }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
    this.logger.info("🛑 SSH service stopped");
  }
};
