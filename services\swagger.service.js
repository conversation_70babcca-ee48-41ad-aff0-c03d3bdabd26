"use strict";

const ApiGateway = require("moleculer-web");
const path = require("path");
const fs = require("fs");

/**
 * Service for Swagger UI
 */
module.exports = {
  name: "$node",
  actions: {
    swagger: {
      handler(ctx) {
        const route = ctx.params.req.$route;
        const basePath = route.path;
        
        const swaggerUIPath = path.resolve(__dirname, "../public/swagger-ui");
        
        // Check if the request is for a static file
        const reqPath = ctx.params.req.url.split("?")[0];
        const filePath = path.join(swaggerUIPath, reqPath);
        
        if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
          // Serve the static file
          return new Promise((resolve, reject) => {
            fs.readFile(filePath, (err, content) => {
              if (err) {
                return reject(err);
              }
              
              const ext = path.extname(filePath);
              let contentType = "text/html";
              
              switch (ext) {
                case ".js":
                  contentType = "text/javascript";
                  break;
                case ".css":
                  contentType = "text/css";
                  break;
                case ".json":
                  contentType = "application/json";
                  break;
                case ".png":
                  contentType = "image/png";
                  break;
                case ".jpg":
                  contentType = "image/jpg";
                  break;
              }
              
              ctx.params.res.writeHead(200, { "Content-Type": contentType });
              ctx.params.res.end(content, "utf-8");
              resolve();
            });
          });
        }
        
        // Serve the Swagger UI HTML
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Moleculer Base API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css">
  <style>
    html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
    *, *:before, *:after { box-sizing: inherit; }
    body { margin: 0; padding: 0; }
    .topbar { display: none; }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: "${basePath}/swagger.json",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout"
      });
      window.ui = ui;
    };
  </script>
</body>
</html>
`;
        
        ctx.params.res.writeHead(200, { "Content-Type": "text/html" });
        ctx.params.res.end(html);
      }
    }
  }
};
