const { getConfig } = require("../../../config/config");
const config = getConfig(process.env.NODE_ENV);

const REGISTER_TEMPLATE = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Moleculer Base!</title>
</head>

<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; padding: 20px; color: white; text-align: center; border-radius: 5px 5px 0 0;">
        <h1 style="margin: 0;">Welcome to Moleculer Base</h1>
    </div>

    <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; border: 1px solid #eee;">
        <p>Dear {userFullname},</p>

        <p>We're excited to let you know that your account has been successfully registered! Welcome to our platform.</p>

        <h2>Account Information:</h2>
        <ul>
            <li><strong>Email:</strong> {account}</li>
        </ul>

        <h2>Getting Started:</h2>
        <ol>
            <li>Click on the <a href="{activateLink}" style="color: #4F46E5; text-decoration: none; font-weight: bold;">Activation Link</a>.</li>
            <li>Use your account information to log in.</li>
            <li>Explore all the features our platform has to offer!</li>
        </ol>

        <div style="background-color: #e8eaf6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h2 style="margin-top: 0;">Need Help?</h2>
            <p>If you need any assistance or have any questions, don't hesitate to contact our support team at <a href="mailto:{supportEmail}" style="color: #4F46E5; text-decoration: none; font-weight: bold;">{supportEmail}</a>.</p>
        </div>

        <p>Thank you for choosing our platform. We're confident you'll have a great experience using our services!</p>

        <p>Best regards,<br>
            The Team
        </p>
    </div>

    <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
        <p>This is an automated email. Please do not reply to this message.</p>
    </div>
</body>

</html>
`;





/**
 * Replace placeholders in a template with actual values
 * @param {String} template - Template string with placeholders
 * @param {Object} replacements - Object with replacement values
 * @returns {String} Template with replaced values
 */
function replacement(template, replacements) {
  return template.replace(
    /{(\w+)}/g,
    (placeholderWithDelimiters, placeholderWithoutDelimiters) =>
      replacements.hasOwnProperty(placeholderWithoutDelimiters) ?
        replacements[placeholderWithoutDelimiters] : placeholderWithDelimiters
  );
}

/**
 * Create registration email
 * @param {Object} userInfo - User information
 * @param {String} activateLink - Activation link
 * @returns {String} HTML email content
 */
exports.createRegisterEmail = (userInfo, activateLink) => {
  const replacements = {
    activateLink: activateLink,
    supportEmail: config.supportEmail || '<EMAIL>',
    account: userInfo.account,
    userFullname: userInfo.fullName,
  };
  return replacement(REGISTER_TEMPLATE, replacements);
};
