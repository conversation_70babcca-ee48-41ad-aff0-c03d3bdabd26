const DbMongoose = require("../../../mixins/dbMongo.mixin");
const REFRESH_TOKEN = require("./refreshToken.model");
const BaseService = require("../../../mixins/baseService.mixin");

module.exports = {
  name: "refreshToken",
  mixins: [Db<PERSON><PERSON>oose(REFRESH_TOKEN), BaseService],
  actions: {
    create: {
      rest: "POST /",
      async handler(ctx) {
        return this.adapter.insert(ctx.params)
      }
    },
    deleteMany: {
      async handler(ctx) {
        return this.adapter.removeMany(ctx.params);
      }
    }
  }
}
