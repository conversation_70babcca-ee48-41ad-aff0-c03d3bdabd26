#!/usr/bin/env node

/**
 * Test script to verify audio playback fixes
 * This script tests the TTS service and audio conversion functionality
 */

const { ServiceBroker } = require("moleculer");
const path = require("path");
const fs = require("fs");

// Create service broker
const broker = new ServiceBroker({
  logger: true,
  logLevel: "info"
});

// Load services
broker.loadService(path.join(__dirname, "services/openai/tts/tts.service.js"));
broker.loadService(path.join(__dirname, "services/files/files.service.js"));

async function testTTSService() {
  console.log("🧪 Starting TTS Service Test...");
  
  try {
    // Start broker
    await broker.start();
    console.log("✅ Broker started successfully");

    // Test TTS generation for Asterisk
    console.log("🎵 Testing TTS generation for Asterisk...");
    
    const testText = "Xin chào! Đây là test audio cho hệ thống Asterisk.";
    
    const result = await broker.call("tts.textToSpeech", {
      text: testText,
      voice: "alloy",
      model: "tts-1",
      speed: 1.0,
      forAsterisk: true
    });

    console.log("🎵 TTS Result:", {
      success: result.success,
      hasFile: !!result.file,
      hasAsteriskPath: !!result.asteriskPath,
      hasSoundName: !!result.soundName,
      error: result.error
    });

    if (result.success) {
      console.log("✅ TTS generation successful!");
      console.log("📁 Asterisk path:", result.asteriskPath);
      console.log("🔊 Sound name:", result.soundName);
      
      // Check if file exists
      if (fs.existsSync(result.asteriskPath)) {
        const stats = fs.statSync(result.asteriskPath);
        console.log("📊 File stats:", {
          size: stats.size,
          created: stats.birthtime
        });
        console.log("✅ Audio file created successfully!");
      } else {
        console.log("❌ Audio file not found at expected path");
      }
    } else {
      console.log("❌ TTS generation failed:", result.error);
    }

    // Test regular TTS (non-Asterisk)
    console.log("\n🎵 Testing regular TTS generation...");
    
    const regularResult = await broker.call("tts.textToSpeech", {
      text: testText,
      voice: "alloy",
      model: "tts-1",
      speed: 1.0,
      forAsterisk: false
    });

    console.log("🎵 Regular TTS Result:", {
      success: regularResult.success,
      hasFile: !!regularResult.file,
      hasFilePath: !!regularResult.filePath,
      error: regularResult.error
    });

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // Stop broker
    await broker.stop();
    console.log("🛑 Broker stopped");
  }
}

async function testAudioConversion() {
  console.log("\n🔄 Testing audio conversion functionality...");
  
  // This would test the conversion methods directly
  // For now, we'll just verify the methods exist
  
  const ttsService = broker.getLocalService("tts");
  if (ttsService) {
    console.log("✅ TTS service loaded");
    
    // Check if conversion methods exist
    const methods = ['convertToAsteriskWav', 'getAsteriskSoundPath', 'generateUniqueAudioFilename'];
    methods.forEach(method => {
      if (typeof ttsService.schema.methods[method] === 'function') {
        console.log(`✅ Method ${method} exists`);
      } else {
        console.log(`❌ Method ${method} missing`);
      }
    });
  }
}

function printTestSummary() {
  console.log("\n" + "=".repeat(60));
  console.log("🧪 AUDIO PLAYBACK FIX TEST SUMMARY");
  console.log("=".repeat(60));
  console.log("✅ Changes implemented:");
  console.log("   • TTS service now generates Asterisk-compatible WAV files");
  console.log("   • Audio conversion from MP3 to WAV (8kHz, 16-bit, mono)");
  console.log("   • Proper file path handling for Asterisk sounds");
  console.log("   • Enhanced error handling and logging");
  console.log("   • Updated ARI handler to use new audio format");
  console.log("\n📋 Key improvements:");
  console.log("   • Audio files saved to Asterisk sounds directory");
  console.log("   • Proper media URI construction for ARI playback");
  console.log("   • Channel state validation before playback");
  console.log("   • Fallback mechanisms for audio failures");
  console.log("\n🎯 Expected results:");
  console.log("   • Default greeting should now be audible to callers");
  console.log("   • AI responses should play correctly");
  console.log("   • Better error handling when audio fails");
  console.log("=".repeat(60));
}

// Run tests
async function runTests() {
  console.log("🚀 Starting Audio Playback Fix Tests");
  console.log("=" .repeat(60));
  
  await testTTSService();
  await testAudioConversion();
  printTestSummary();
  
  console.log("\n✅ Tests completed!");
  console.log("💡 To test with actual calls, restart the ARI handler service");
  console.log("   and make a test call to verify audio playback works.");
}

// Handle script execution
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testTTSService,
  testAudioConversion,
  runTests
};
