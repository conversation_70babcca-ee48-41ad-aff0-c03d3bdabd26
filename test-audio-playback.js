#!/usr/bin/env node

/**
 * Simple test for audio playback with ARI
 * This script tests different media URI formats
 */

const AriClient = require("ari-client");

// ARI connection settings (adjust as needed)
const ARI_CONFIG = {
  url: process.env.ARI_URL || "http://***************:8088",
  username: process.env.ARI_USERNAME || "ariuser", 
  password: process.env.ARI_PASSWORD || "hE8CNBPi9p98Dv9Q",
  app: process.env.ARI_APP || "voicebot"
};

async function testAudioPlayback() {
  console.log("🧪 Testing Audio Playback with ARI");
  console.log("==================================");
  
  try {
    // Connect to ARI
    console.log("🔌 Connecting to ARI...");
    const client = await AriClient.connect(
      ARI_CONFIG.url,
      ARI_CONFIG.username, 
      ARI_CONFIG.password
    );
    
    console.log("✅ Connected to ARI successfully");
    
    // Set up event handler for incoming calls
    client.on("StasisStart", async (event, channel) => {
      const caller = channel.caller.number || "Unknown";
      console.log(`📞 Incoming call from: ${caller}`);
      
      try {
        // Answer the call
        await channel.answer();
        console.log("✅ Call answered");
        
        // Test different audio formats
        const testSounds = [
          // Built-in Asterisk sounds (should work)
          "sound:beep",
          "sound:hello", 
          "sound:welcome",
          
          // Custom sounds (our TTS files)
          "sound:custom/tts_test",
          "sound:/var/lib/asterisk/sounds/custom/tts_test",
          
          // Try the latest TTS file
          "sound:custom/tts_1752463592591_u662h9"
        ];
        
        for (const [index, soundUri] of testSounds.entries()) {
          console.log(`🔊 Testing sound ${index + 1}/${testSounds.length}: ${soundUri}`);
          
          try {
            const playback = await channel.play({
              media: soundUri
            });
            
            console.log(`✅ Playback started: ${playback.id}`);
            
            // Wait for playback to finish
            await new Promise((resolve) => {
              const onFinished = (event) => {
                if (event.playback.id === playback.id) {
                  console.log(`✅ Playback finished: ${playback.id}`);
                  client.removeListener('PlaybackFinished', onFinished);
                  resolve();
                }
              };
              
              client.on('PlaybackFinished', onFinished);
              
              // Timeout after 10 seconds
              setTimeout(() => {
                console.log(`⏰ Playback timeout: ${playback.id}`);
                client.removeListener('PlaybackFinished', onFinished);
                resolve();
              }, 10000);
            });
            
            // Wait between tests
            await new Promise(resolve => setTimeout(resolve, 1000));
            
          } catch (playError) {
            console.log(`❌ Playback failed: ${soundUri} - ${playError.message}`);
          }
        }
        
        // End the call
        console.log("📞 Ending test call");
        await channel.hangup();
        
      } catch (error) {
        console.error("❌ Error during call handling:", error);
        try {
          await channel.hangup();
        } catch (hangupError) {
          console.error("❌ Error hanging up:", hangupError);
        }
      }
    });
    
    // Start the ARI application
    await client.start(ARI_CONFIG.app);
    console.log(`🚀 ARI application '${ARI_CONFIG.app}' started`);
    console.log("📞 Waiting for incoming calls...");
    console.log("💡 Make a test call to see audio playback results");
    console.log("⏹️  Press Ctrl+C to stop");
    
    // Keep the script running
    process.on('SIGINT', async () => {
      console.log("\n🛑 Stopping test...");
      process.exit(0);
    });
    
  } catch (error) {
    console.error("❌ Failed to connect to ARI:", error);
    process.exit(1);
  }
}

function printInstructions() {
  console.log("\n" + "=".repeat(50));
  console.log("📋 TEST INSTRUCTIONS");
  console.log("=".repeat(50));
  console.log("1. Make sure ARI handler service is stopped");
  console.log("2. Run this test script: node test-audio-playback.js");
  console.log("3. Make a test call to your Asterisk system");
  console.log("4. Listen for different audio playback attempts");
  console.log("5. Check console output for success/failure");
  console.log("\n💡 This will help identify which audio URI format works");
  console.log("=".repeat(50));
}

// Run test if script is executed directly
if (require.main === module) {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    printInstructions();
  } else {
    testAudioPlayback().catch(console.error);
  }
}

module.exports = { testAudioPlayback };
