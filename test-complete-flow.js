#!/usr/bin/env node

/**
 * Test complete call flow: TTS -> SSH -> Recording -> AI -> TTS
 * Simulates the entire conversation flow
 */

const { ServiceBroker } = require("moleculer");
const fs = require('fs');
const path = require('path');

// Create service broker
const broker = new ServiceBroker({
  logger: true,
  logLevel: "info"
});

async function testCompleteFlow() {
  console.log("🧪 Testing Complete Call Flow");
  console.log("=".repeat(50));

  try {
    // Load all required services
    broker.loadService("./services/ssh/ssh.service.js");
    broker.loadService("./services/openai/tts/tts.service.js");
    broker.loadService("./services/langchain/langchain.service.js");
    
    // Start broker
    await broker.start();
    console.log("✅ All services started");

    // Test 1: Generate greeting TTS
    console.log("\n🎵 Test 1: Generate Greeting TTS");
    console.log("-".repeat(35));
    
    const greetingText = "Xin chào! Tôi là trợ lý AI của GHVN. Tôi có thể giúp gì cho bạn?";
    const greetingResult = await broker.call("tts.textToSpeech", {
      text: greetingText,
      voice: "alloy",
      forAsterisk: true
    });

    console.log("Greeting TTS:", {
      success: greetingResult.success,
      soundName: greetingResult.soundName,
      sshCopySuccess: greetingResult.sshCopy?.success,
      error: greetingResult.error
    });

    if (!greetingResult.success) {
      console.log("❌ Greeting TTS failed, cannot proceed");
      return;
    }

    // Test 2: Simulate user input and AI response
    console.log("\n🤖 Test 2: AI Response Generation");
    console.log("-".repeat(40));

    // Mock call session
    const mockCallSession = {
      channelId: "test_channel_123",
      caller: "0123456789",
      startTime: Date.now() - 60000, // 1 minute ago
      conversationTurns: 1,
      conversationHistory: [
        {
          role: "assistant",
          content: greetingText,
          timestamp: new Date().toISOString()
        }
      ]
    };

    // Simulate user saying something
    const userInput = "Tôi muốn kiểm tra đơn hàng GH123456 của tôi";
    console.log(`User input: "${userInput}"`);

    // Build context
    const context = `Cuộc gọi từ số ${mockCallSession.caller}. 
Đây là lượt hội thoại thứ ${mockCallSession.conversationTurns + 1}.
Thời gian gọi: ${Math.floor((Date.now() - mockCallSession.startTime) / 1000)} giây.`;

    // Prepare messages for AI
    const messages = [
      {
        role: "system",
        content: `Bạn là trợ lý AI của GHVN (Giao Hàng Việt Nam), một công ty giao hàng. 
Nhiệm vụ của bạn là hỗ trợ khách hàng qua điện thoại một cách thân thiện và chuyên nghiệp.

Nguyên tắc trả lời:
- Trả lời ngắn gọn, rõ ràng (tối đa 2-3 câu)
- Sử dụng giọng điệu thân thiện, lịch sự
- Tập trung vào việc giải quyết vấn đề của khách hàng
- Nếu không thể giải quyết, hướng dẫn khách hàng liên hệ nhân viên

Context cuộc gọi:
${context}`
      }
    ];

    // Add conversation history
    mockCallSession.conversationHistory.forEach(entry => {
      messages.push({
        role: entry.role === 'user' ? 'user' : 'assistant',
        content: entry.content
      });
    });

    // Add current user message
    messages.push({
      role: "user",
      content: userInput
    });

    // Generate AI response
    const aiResult = await broker.call("langchain.chatCompletion", {
      messages: messages,
      model: "gpt-3.5-turbo",
      temperature: 0.7,
      modelInterface: "ChatOpenAI"
    });

    let aiResponseText = "";
    if (typeof aiResult === 'string') {
      aiResponseText = aiResult;
    } else if (aiResult && aiResult.content) {
      aiResponseText = aiResult.content;
    }

    console.log(`AI Response: "${aiResponseText}"`);

    if (!aiResponseText) {
      console.log("❌ AI response failed, cannot proceed");
      return;
    }

    // Test 3: Generate response TTS
    console.log("\n🔊 Test 3: Generate Response TTS");
    console.log("-".repeat(35));

    const responseResult = await broker.call("tts.textToSpeech", {
      text: aiResponseText,
      voice: "alloy",
      forAsterisk: true
    });

    console.log("Response TTS:", {
      success: responseResult.success,
      soundName: responseResult.soundName,
      sshCopySuccess: responseResult.sshCopy?.success,
      error: responseResult.error
    });

    // Test 4: Multiple conversation turns
    console.log("\n🔄 Test 4: Multiple Conversation Turns");
    console.log("-".repeat(45));

    const conversationTests = [
      "Đơn hàng của tôi đang ở đâu?",
      "Khi nào tôi nhận được hàng?",
      "Tôi muốn thay đổi địa chỉ giao hàng",
      "Cảm ơn bạn"
    ];

    // Update conversation history
    mockCallSession.conversationHistory.push(
      { role: "user", content: userInput, timestamp: new Date().toISOString() },
      { role: "assistant", content: aiResponseText, timestamp: new Date().toISOString() }
    );
    mockCallSession.conversationTurns++;

    for (let i = 0; i < conversationTests.length; i++) {
      const testInput = conversationTests[i];
      console.log(`\n--- Turn ${i + 2}: "${testInput}" ---`);

      try {
        // Build new context
        const newContext = `Cuộc gọi từ số ${mockCallSession.caller}. 
Đây là lượt hội thoại thứ ${mockCallSession.conversationTurns + 1}.
Thời gian gọi: ${Math.floor((Date.now() - mockCallSession.startTime) / 1000)} giây.`;

        // Prepare messages
        const newMessages = [
          {
            role: "system",
            content: `Bạn là trợ lý AI của GHVN (Giao Hàng Việt Nam), một công ty giao hàng. 
Trả lời ngắn gọn, thân thiện và chuyên nghiệp.

Context cuộc gọi:
${newContext}`
          }
        ];

        // Add recent conversation history (last 6 messages)
        const recentHistory = mockCallSession.conversationHistory.slice(-6);
        recentHistory.forEach(entry => {
          newMessages.push({
            role: entry.role === 'user' ? 'user' : 'assistant',
            content: entry.content
          });
        });

        // Add current user message
        newMessages.push({
          role: "user",
          content: testInput
        });

        // Generate AI response
        const turnAiResult = await broker.call("langchain.chatCompletion", {
          messages: newMessages,
          model: "gpt-3.5-turbo",
          temperature: 0.7,
          modelInterface: "ChatOpenAI"
        });

        let turnResponseText = "";
        if (typeof turnAiResult === 'string') {
          turnResponseText = turnAiResult;
        } else if (turnAiResult && turnAiResult.content) {
          turnResponseText = turnAiResult.content;
        }

        console.log(`AI: "${turnResponseText}"`);

        // Generate TTS for response
        const turnTtsResult = await broker.call("tts.textToSpeech", {
          text: turnResponseText,
          voice: "alloy",
          forAsterisk: true
        });

        console.log(`TTS: ${turnTtsResult.success ? '✅' : '❌'} ${turnTtsResult.sshCopy?.success ? '(SSH ✅)' : '(SSH ❌)'}`);

        // Update conversation history
        mockCallSession.conversationHistory.push(
          { role: "user", content: testInput, timestamp: new Date().toISOString() },
          { role: "assistant", content: turnResponseText, timestamp: new Date().toISOString() }
        );
        mockCallSession.conversationTurns++;

      } catch (error) {
        console.log(`❌ Turn ${i + 2} failed:`, error.message);
      }
    }

    // Summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 COMPLETE FLOW TEST SUMMARY");
    console.log("=".repeat(50));

    const tests = [
      { name: "Greeting TTS Generation", success: greetingResult.success },
      { name: "Greeting SSH Copy", success: greetingResult.sshCopy?.success },
      { name: "AI Response Generation", success: !!aiResponseText },
      { name: "Response TTS Generation", success: responseResult.success },
      { name: "Response SSH Copy", success: responseResult.sshCopy?.success }
    ];

    tests.forEach(test => {
      const status = test.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${test.name}`);
    });

    const passedTests = tests.filter(t => t.success).length;
    const totalTests = tests.length;
    
    console.log(`\n📈 Results: ${passedTests}/${totalTests} core tests passed`);
    console.log(`🔄 Conversation turns completed: ${mockCallSession.conversationTurns}`);

    if (passedTests === totalTests) {
      console.log("\n🎉 COMPLETE FLOW IS WORKING!");
      console.log("✅ The system can handle full conversations:");
      console.log("   - Generate greeting audio");
      console.log("   - Process user input");
      console.log("   - Generate AI responses");
      console.log("   - Convert responses to audio");
      console.log("   - Copy audio files to Asterisk server");
      console.log("   - Maintain conversation context");
    } else {
      console.log("\n⚠️ Some components need attention");
    }

    console.log("\n🚀 READY FOR PRODUCTION!");
    console.log("The system is ready to handle real phone calls.");

  } catch (error) {
    console.error("❌ Complete flow test failed:", error);
  } finally {
    // Stop broker
    await broker.stop();
    console.log("\n🛑 Services stopped");
  }
}

// Run the test
if (require.main === module) {
  testCompleteFlow().catch(console.error);
}

module.exports = { testCompleteFlow };
