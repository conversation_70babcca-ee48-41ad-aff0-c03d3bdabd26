#!/usr/bin/env node

/**
 * Test script for External Media API
 * Tests direct ARI External Media channel creation
 */

const AriClient = require("ari-client");

async function testExternalMedia() {
  console.log("🧪 Testing External Media API");
  console.log("=".repeat(50));

  try {
    // Connect to ARI
    console.log("📡 Connecting to ARI...");
    const ari = await AriClient.connect(
      process.env.ARI_URL || "http://***************:8088",
      process.env.ARI_USERNAME || "ariuser", 
      process.env.ARI_PASSWORD || "hE8CNBPi9p98Dv9Q"
    );

    console.log("✅ ARI connected successfully");

    // Test 1: Check if externalMedia method exists
    console.log("\n🔍 Test 1: Check External Media API availability");
    console.log("-".repeat(50));
    
    if (typeof ari.channels.externalMedia === 'function') {
      console.log("✅ External Media API is available");
    } else {
      console.log("❌ External Media API is NOT available");
      console.log("Available channel methods:", Object.keys(ari.channels));
      return;
    }

    // Test 2: Create External Media channel
    console.log("\n🌐 Test 2: Create External Media Channel");
    console.log("-".repeat(45));

    const externalMediaParams = {
      app: process.env.ARI_APP || "voicebot",
      external_host: "127.0.0.1:60000",
      format: "ulaw"
    };

    console.log("Parameters:", externalMediaParams);

    try {
      const externalChannel = await ari.channels.externalMedia(externalMediaParams);
      
      console.log("✅ External Media channel created successfully!");
      console.log("Channel ID:", externalChannel.id);
      console.log("Channel name:", externalChannel.name);
      console.log("Channel state:", externalChannel.state);

      // Test 3: Get channel details
      console.log("\n📋 Test 3: Get Channel Details");
      console.log("-".repeat(35));

      const channelDetails = await ari.channels.get({ channelId: externalChannel.id });
      console.log("Channel details:", {
        id: channelDetails.id,
        name: channelDetails.name,
        state: channelDetails.state,
        caller: channelDetails.caller,
        connected: channelDetails.connected,
        creationtime: channelDetails.creationtime
      });

      // Test 4: List all channels
      console.log("\n📋 Test 4: List All Channels");
      console.log("-".repeat(30));

      const allChannels = await ari.channels.list();
      console.log(`Total channels: ${allChannels.length}`);
      
      const externalChannels = allChannels.filter(ch => ch.name && ch.name.includes('External'));
      console.log(`External Media channels: ${externalChannels.length}`);

      if (externalChannels.length > 0) {
        externalChannels.forEach(ch => {
          console.log(`  - ${ch.id}: ${ch.name} (${ch.state})`);
        });
      }

      // Test 5: Clean up - Hangup the channel
      console.log("\n🧹 Test 5: Cleanup External Media Channel");
      console.log("-".repeat(45));

      await ari.channels.hangup({ channelId: externalChannel.id });
      console.log("✅ External Media channel hung up successfully");

    } catch (createError) {
      console.log("❌ Failed to create External Media channel:");
      console.log("Error:", createError.message);
      console.log("Error details:", createError);
      
      // Check if it's a parameter issue
      if (createError.message.includes('400') || createError.message.includes('Bad Request')) {
        console.log("\n💡 This might be a parameter issue. Let's try with minimal parameters:");
        
        try {
          const minimalParams = {
            app: process.env.ARI_APP || "voicebot",
            external_host: "127.0.0.1:60000",
            format: "ulaw"
          };
          
          console.log("Minimal parameters:", minimalParams);
          const minimalChannel = await ari.channels.externalMedia(minimalParams);
          console.log("✅ Minimal External Media channel created:", minimalChannel.id);
          
          // Clean up
          await ari.channels.hangup({ channelId: minimalChannel.id });
          console.log("✅ Minimal channel cleaned up");
          
        } catch (minimalError) {
          console.log("❌ Minimal parameters also failed:", minimalError.message);
        }
      }
    }

    // Test 6: Check Asterisk version and capabilities
    console.log("\n📊 Test 6: Check Asterisk Info");
    console.log("-".repeat(35));

    try {
      const asteriskInfo = await ari.asterisk.getInfo();
      console.log("Asterisk version:", asteriskInfo.version);
      console.log("Build info:", asteriskInfo.build);
      
      // Check if External Media is supported in this version
      if (asteriskInfo.version) {
        const version = asteriskInfo.version;
        console.log(`Asterisk version: ${version}`);
        
        // External Media was introduced in Asterisk 16.6
        if (version.includes('16.') || version.includes('17.') || version.includes('18.') || version.includes('19.') || version.includes('20.')) {
          console.log("✅ This Asterisk version should support External Media");
        } else {
          console.log("⚠️ This Asterisk version might not support External Media (requires 16.6+)");
        }
      }
    } catch (infoError) {
      console.log("⚠️ Could not get Asterisk info:", infoError.message);
    }

    // Summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 EXTERNAL MEDIA TEST SUMMARY");
    console.log("=".repeat(50));
    console.log("✅ ARI connection: Working");
    console.log("✅ External Media API: Available");
    console.log("🔄 Channel creation: Check logs above");

    console.log("\n💡 TROUBLESHOOTING TIPS:");
    console.log("1. Ensure Asterisk version is 16.6 or higher");
    console.log("2. Check if External Media module is loaded:");
    console.log("   asterisk -rx 'module show like res_ari'");
    console.log("3. Verify ARI application is configured in Asterisk");
    console.log("4. Check Asterisk logs for detailed error messages");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log("\n💡 Connection refused - check:");
      console.log("1. Asterisk is running");
      console.log("2. ARI is enabled in http.conf");
      console.log("3. ARI URL and credentials are correct");
    }
  }
}

// Helper function to check environment variables
function checkEnvironment() {
  console.log("🔍 Environment Variables:");
  console.log("-".repeat(30));
  
  const vars = [
    'ARI_URL',
    'ARI_USERNAME', 
    'ARI_PASSWORD',
    'ARI_APP'
  ];

  vars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      const displayValue = varName.includes('PASSWORD') 
        ? `${value.substring(0, 4)}...`
        : value;
      console.log(`✅ ${varName}: ${displayValue}`);
    } else {
      console.log(`⚪ ${varName}: Using default`);
    }
  });
  console.log("");
}

// Run the test
if (require.main === module) {
  checkEnvironment();
  testExternalMedia().catch(console.error);
}

module.exports = { testExternalMedia };
