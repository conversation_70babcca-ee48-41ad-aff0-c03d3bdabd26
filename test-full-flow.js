#!/usr/bin/env node

/**
 * Test script for full External Media + Audio Stream flow
 * Tests the complete integration between ARI and Audio Stream service
 */

const AriClient = require("ari-client");

async function testFullFlow() {
  console.log("🧪 Testing Full External Media + Audio Stream Flow");
  console.log("=".repeat(60));

  try {
    // Connect to ARI
    console.log("📡 Connecting to ARI...");
    const ari = await AriClient.connect(
      process.env.ARI_URL || "http://***************:8088",
      process.env.ARI_USERNAME || "ariuser", 
      process.env.ARI_PASSWORD || "hE8CNBPi9p98Dv9Q"
    );

    console.log("✅ ARI connected successfully");

    // Test 1: Simulate Audio Stream Service Response
    console.log("\n🎵 Test 1: Simulate Audio Stream Service");
    console.log("-".repeat(50));

    const mockStreamResult = {
      success: true,
      udpHost: "127.0.0.1",
      udpPort: 60000,
      format: "ulaw",
      sessionId: "test-session-" + Date.now()
    };

    console.log("Mock stream result:", mockStreamResult);

    // Test 2: Create External Media Channel (like in ARI Handler)
    console.log("\n🌐 Test 2: Create External Media Channel");
    console.log("-".repeat(45));

    const externalMediaParams = {
      app: process.env.ARI_APP || "voicebot",
      external_host: `${mockStreamResult.udpHost}:${mockStreamResult.udpPort}`,
      format: mockStreamResult.format
    };

    console.log("External Media parameters:", externalMediaParams);

    const externalMediaChannel = await ari.channels.externalMedia(externalMediaParams);
    
    console.log("✅ External Media channel created successfully!");
    console.log("Channel ID:", externalMediaChannel.id);
    console.log("Channel name:", externalMediaChannel.name);
    console.log("Channel state:", externalMediaChannel.state);

    // Test 3: Create a Bridge (like in ARI Handler)
    console.log("\n🌉 Test 3: Create Bridge");
    console.log("-".repeat(30));

    const bridge = await ari.bridges.create({
      type: "mixing",
      name: `test-bridge-${Date.now()}`
    });

    console.log("✅ Bridge created successfully!");
    console.log("Bridge ID:", bridge.id);
    console.log("Bridge name:", bridge.name);

    // Test 4: Add External Media Channel to Bridge
    console.log("\n🔗 Test 4: Add External Media to Bridge");
    console.log("-".repeat(45));

    await bridge.addChannel({
      channel: externalMediaChannel.id
    });

    console.log("✅ External Media channel added to bridge successfully!");

    // Test 5: Check Bridge Status
    console.log("\n📊 Test 5: Check Bridge Status");
    console.log("-".repeat(35));

    const bridgeDetails = await ari.bridges.get({ bridgeId: bridge.id });
    console.log("Bridge details:", {
      id: bridgeDetails.id,
      name: bridgeDetails.name,
      bridge_type: bridgeDetails.bridge_type,
      channels: bridgeDetails.channels.map(ch => ({ id: ch, name: ch }))
    });

    console.log(`Bridge has ${bridgeDetails.channels.length} channel(s)`);

    // Test 6: Simulate some operations
    console.log("\n⚡ Test 6: Simulate Operations");
    console.log("-".repeat(35));

    // Wait a bit to simulate real usage
    console.log("⏳ Waiting 2 seconds to simulate real usage...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check channel state
    const channelDetails = await ari.channels.get({ channelId: externalMediaChannel.id });
    console.log("Channel state after 2 seconds:", channelDetails.state);

    // Test 7: Cleanup
    console.log("\n🧹 Test 7: Cleanup");
    console.log("-".repeat(25));

    // Remove channel from bridge
    console.log("🔌 Removing channel from bridge...");
    await bridge.removeChannel({
      channel: externalMediaChannel.id
    });
    console.log("✅ Channel removed from bridge");

    // Hangup External Media channel
    console.log("📞 Hanging up External Media channel...");
    await ari.channels.hangup({ channelId: externalMediaChannel.id });
    console.log("✅ External Media channel hung up");

    // Destroy bridge
    console.log("🌉 Destroying bridge...");
    await ari.bridges.destroy({ bridgeId: bridge.id });
    console.log("✅ Bridge destroyed");

    // Test 8: Verify Cleanup
    console.log("\n✅ Test 8: Verify Cleanup");
    console.log("-".repeat(30));

    try {
      await ari.channels.get({ channelId: externalMediaChannel.id });
      console.log("⚠️ Channel still exists (unexpected)");
    } catch (error) {
      if (error.message.includes('404')) {
        console.log("✅ Channel properly cleaned up (404 as expected)");
      } else {
        console.log("⚠️ Unexpected error checking channel:", error.message);
      }
    }

    try {
      await ari.bridges.get({ bridgeId: bridge.id });
      console.log("⚠️ Bridge still exists (unexpected)");
    } catch (error) {
      if (error.message.includes('404')) {
        console.log("✅ Bridge properly cleaned up (404 as expected)");
      } else {
        console.log("⚠️ Unexpected error checking bridge:", error.message);
      }
    }

    // Summary
    console.log("\n" + "=".repeat(60));
    console.log("🎉 FULL FLOW TEST SUMMARY");
    console.log("=".repeat(60));
    console.log("✅ ARI connection: Working");
    console.log("✅ External Media creation: Working");
    console.log("✅ Bridge creation: Working");
    console.log("✅ Channel-Bridge integration: Working");
    console.log("✅ Cleanup: Working");

    console.log("\n💡 INTEGRATION NOTES:");
    console.log("1. External Media API is fully functional");
    console.log("2. Bridge integration works correctly");
    console.log("3. Channel lifecycle management is working");
    console.log("4. Ready for real Audio Stream service integration");

    console.log("\n🔄 NEXT STEPS:");
    console.log("1. Start the main application with npm run dev");
    console.log("2. Make a test call to trigger External Media flow");
    console.log("3. Check logs for External Media channel creation");
    console.log("4. Verify Audio Stream service receives UDP traffic");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log("\n💡 Connection refused - check:");
      console.log("1. Asterisk is running");
      console.log("2. ARI is enabled in http.conf");
      console.log("3. ARI URL and credentials are correct");
    } else if (error.message.includes('404')) {
      console.log("\n💡 404 error - check:");
      console.log("1. ARI application 'voicebot' is configured in Asterisk");
      console.log("2. Application is running and accepting connections");
    } else {
      console.log("\n💡 Error details:", error);
    }
  }
}

// Helper function to check environment variables
function checkEnvironment() {
  console.log("🔍 Environment Variables:");
  console.log("-".repeat(30));
  
  const vars = [
    'ARI_URL',
    'ARI_USERNAME', 
    'ARI_PASSWORD',
    'ARI_APP'
  ];

  vars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      const displayValue = varName.includes('PASSWORD') 
        ? `${value.substring(0, 4)}...`
        : value;
      console.log(`✅ ${varName}: ${displayValue}`);
    } else {
      console.log(`⚪ ${varName}: Using default`);
    }
  });
  console.log("");
}

// Run the test
if (require.main === module) {
  checkEnvironment();
  testFullFlow().catch(console.error);
}

module.exports = { testFullFlow };
