#!/usr/bin/env node

/**
 * Test script for recording flow with SSH copy
 * Tests the complete flow: recording -> SSH download -> transcription -> cleanup
 */

const { ServiceBroker } = require("moleculer");
const fs = require('fs');
const path = require('path');

// Create service broker
const broker = new ServiceBroker({
  logger: true,
  logLevel: "info"
});

async function testRecordingFlow() {
  console.log("🧪 Testing Recording Flow with SSH Copy");
  console.log("=".repeat(50));

  try {
    // Load services
    broker.loadService("./services/ssh/ssh.service.js");
    broker.loadService("./services/openai/whisper/whisper.service.js");
    
    // Start broker
    await broker.start();
    console.log("✅ Services started");

    // Test 1: SSH Connection
    console.log("\n📡 Test 1: SSH Connection Test");
    console.log("-".repeat(30));
    
    const connectionTest = await broker.call("ssh.testConnection");
    console.log("SSH Connection:", connectionTest.success ? "✅ OK" : "❌ FAILED");
    
    if (!connectionTest.success) {
      console.log("❌ SSH connection failed, cannot proceed with tests");
      return;
    }

    // Test 2: Create a mock recording file on remote server
    console.log("\n📁 Test 2: Create Mock Recording File");
    console.log("-".repeat(40));
    
    const mockRecordingName = `test_recording_${Date.now()}`;
    const remoteRecordingPath = `/var/spool/asterisk/recording/${mockRecordingName}.wav`;
    const localRecordingPath = path.join(__dirname, "services/ari-handler/recordings", `${mockRecordingName}.wav`);
    
    // Create a small test WAV file locally first
    const testWavContent = Buffer.from([
      0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00, 0x57, 0x41, 0x56, 0x45, 0x66, 0x6d, 0x74, 0x20,
      0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x44, 0xac, 0x00, 0x00, 0x88, 0x58, 0x01, 0x00,
      0x02, 0x00, 0x10, 0x00, 0x64, 0x61, 0x74, 0x61, 0x00, 0x00, 0x00, 0x00
    ]);
    
    const tempLocalFile = path.join(__dirname, `temp_${mockRecordingName}.wav`);
    fs.writeFileSync(tempLocalFile, testWavContent);
    console.log(`Created test WAV file: ${tempLocalFile}`);

    // Upload test file to simulate recording
    const uploadResult = await broker.call("ssh.copyFileToAsterisk", {
      localFilePath: tempLocalFile,
      remoteFileName: `${mockRecordingName}.wav`,
      remoteSubDir: "../recording" // Go to /var/spool/asterisk/recording
    });

    console.log("Mock recording upload:", uploadResult.success ? "✅ OK" : "❌ FAILED");
    
    if (!uploadResult.success) {
      console.log("❌ Failed to create mock recording, cannot proceed");
      fs.unlinkSync(tempLocalFile);
      return;
    }

    // Test 3: Download recording file (simulate ARI Handler behavior)
    console.log("\n📥 Test 3: Download Recording File");
    console.log("-".repeat(40));
    
    const downloadResult = await broker.call("ssh.downloadFileFromAsterisk", {
      remoteFilePath: remoteRecordingPath,
      localFilePath: localRecordingPath
    });

    console.log("Recording download:", downloadResult.success ? "✅ OK" : "❌ FAILED");
    console.log("File size:", downloadResult.fileSize, "bytes");
    console.log("Transfer time:", downloadResult.transferTime, "ms");

    if (downloadResult.success) {
      // Verify local file exists
      const localExists = fs.existsSync(localRecordingPath);
      console.log("Local file exists:", localExists ? "✅ YES" : "❌ NO");
      
      if (localExists) {
        const localStats = fs.statSync(localRecordingPath);
        console.log("Local file size:", localStats.size, "bytes");
      }
    }

    // Test 4: Test Whisper transcription (if file exists)
    if (downloadResult.success && fs.existsSync(localRecordingPath)) {
      console.log("\n🎙️ Test 4: Whisper Transcription");
      console.log("-".repeat(35));
      
      try {
        const transcriptionResult = await broker.call("whisper.transcriptAudio", {
          audioPath: localRecordingPath,
          model: "whisper-1"
        });

        console.log("Transcription result:", {
          hasText: !!transcriptionResult.text,
          text: transcriptionResult.text || "No text",
          error: transcriptionResult.error
        });
      } catch (transcriptionError) {
        console.log("⚠️ Transcription test skipped (service may not be available):", transcriptionError.message);
      }
    }

    // Test 5: Cleanup files
    console.log("\n🧹 Test 5: Cleanup Files");
    console.log("-".repeat(25));
    
    // Cleanup local file
    if (fs.existsSync(localRecordingPath)) {
      fs.unlinkSync(localRecordingPath);
      console.log("✅ Local recording file cleaned up");
    }

    // Cleanup remote file
    const cleanupResult = await broker.call("ssh.cleanupRemoteFile", {
      remoteFilePath: remoteRecordingPath
    });
    console.log("Remote cleanup:", cleanupResult.success ? "✅ OK" : "❌ FAILED");

    // Cleanup temp file
    if (fs.existsSync(tempLocalFile)) {
      fs.unlinkSync(tempLocalFile);
      console.log("✅ Temp file cleaned up");
    }

    // Summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 TEST SUMMARY");
    console.log("=".repeat(50));

    const tests = [
      { name: "SSH Connection", success: connectionTest.success },
      { name: "Mock Recording Upload", success: uploadResult.success },
      { name: "Recording Download", success: downloadResult.success },
      { name: "File Cleanup", success: cleanupResult.success }
    ];

    tests.forEach(test => {
      const status = test.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${test.name}`);
    });

    const passedTests = tests.filter(t => t.success).length;
    const totalTests = tests.length;
    
    console.log(`\n📈 Results: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log("🎉 All tests passed! Recording flow is working correctly.");
      console.log("\n✅ The system can now:");
      console.log("   - Download recording files from Asterisk server");
      console.log("   - Process them locally for transcription");
      console.log("   - Clean up both local and remote files");
    } else {
      console.log("⚠️ Some tests failed. Please check the configuration.");
    }

    console.log("\n🔧 NEXT STEPS");
    console.log("-".repeat(15));
    console.log("1. Test with actual ARI Handler service");
    console.log("2. Make a real phone call to test end-to-end");
    console.log("3. Monitor logs for any issues in production");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
  } finally {
    // Stop broker
    await broker.stop();
    console.log("\n🛑 Services stopped");
  }
}

// Run the test
if (require.main === module) {
  testRecordingFlow().catch(console.error);
}

module.exports = { testRecordingFlow };
