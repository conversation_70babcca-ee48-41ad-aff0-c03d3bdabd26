#!/usr/bin/env node

/**
 * Test script for SSH copy functionality
 * This script tests the SSH service and TTS integration
 */

const { ServiceBroker } = require("moleculer");
const fs = require('fs');
const path = require('path');

// Create service broker
const broker = new ServiceBroker({
  logger: true,
  logLevel: "info"
});

// Load services
broker.loadService("./services/ssh/ssh.service.js");
broker.loadService("./services/openai/tts/tts.service.js");

async function testSSHCopy() {
  console.log("🧪 Testing SSH Copy Functionality");
  console.log("=".repeat(50));

  try {
    // Start broker
    await broker.start();
    console.log("✅ Broker started");

    // Test 1: SSH Connection Test
    console.log("\n📡 Test 1: SSH Connection Test");
    console.log("-".repeat(30));
    
    const connectionTest = await broker.call("ssh.testConnection");
    console.log("Connection test result:", {
      success: connectionTest.success,
      host: connectionTest.host,
      directoryExists: connectionTest.directoryExists,
      error: connectionTest.error
    });

    if (!connectionTest.success) {
      console.log("❌ SSH connection failed. Please check your SSH credentials.");
      return;
    }

    // Test 2: Create test audio file and copy
    console.log("\n🎵 Test 2: TTS Generation and SSH Copy");
    console.log("-".repeat(40));

    const testText = "Xin chào! Đây là test SSH copy functionality.";
    console.log(`Generating TTS for: "${testText}"`);

    const ttsResult = await broker.call("tts.textToSpeech", {
      text: testText,
      voice: "alloy",
      model: "tts-1",
      speed: 1.0,
      forAsterisk: true
    });

    console.log("TTS Result:", {
      success: ttsResult.success,
      hasFile: !!ttsResult.file,
      soundName: ttsResult.soundName,
      localPath: ttsResult.asteriskPath,
      sshCopySuccess: ttsResult.sshCopy?.success,
      remoteFilePath: ttsResult.sshCopy?.remoteFilePath,
      transferTime: ttsResult.sshCopy?.transferTime,
      error: ttsResult.error || ttsResult.sshCopy?.error
    });

    if (!ttsResult.success) {
      console.log("❌ TTS generation failed:", ttsResult.error);
      return;
    }

    // Test 3: Manual SSH copy test
    console.log("\n📁 Test 3: Manual SSH Copy Test");
    console.log("-".repeat(35));

    if (ttsResult.asteriskPath && fs.existsSync(ttsResult.asteriskPath)) {
      const manualCopyResult = await broker.call("ssh.copyFileToAsterisk", {
        localFilePath: ttsResult.asteriskPath,
        remoteFileName: `manual_test_${Date.now()}.wav`,
        remoteSubDir: "test"
      });

      console.log("Manual copy result:", {
        success: manualCopyResult.success,
        localFile: manualCopyResult.localFilePath,
        remoteFile: manualCopyResult.remoteFilePath,
        fileSize: manualCopyResult.fileSize,
        transferTime: manualCopyResult.transferTime,
        error: manualCopyResult.error
      });
    }

    // Test 4: Directory creation test
    console.log("\n📂 Test 4: Remote Directory Creation Test");
    console.log("-".repeat(45));

    const testDir = "/var/lib/asterisk/sounds/test_dir_" + Date.now();
    const dirResult = await broker.call("ssh.ensureRemoteDirectory", {
      remoteDir: testDir
    });

    console.log("Directory creation result:", {
      success: dirResult.success,
      remoteDir: dirResult.remoteDir,
      created: dirResult.created,
      error: dirResult.error
    });

    // Summary
    console.log("\n" + "=".repeat(50));
    console.log("📊 TEST SUMMARY");
    console.log("=".repeat(50));

    const tests = [
      { name: "SSH Connection", success: connectionTest.success },
      { name: "TTS Generation", success: ttsResult.success },
      { name: "Auto SSH Copy", success: ttsResult.sshCopy?.success || false },
      { name: "Manual SSH Copy", success: manualCopyResult?.success || false },
      { name: "Directory Creation", success: dirResult.success }
    ];

    tests.forEach(test => {
      const status = test.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${test.name}`);
    });

    const passedTests = tests.filter(t => t.success).length;
    const totalTests = tests.length;
    
    console.log(`\n📈 Results: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log("🎉 All tests passed! SSH copy functionality is working correctly.");
    } else {
      console.log("⚠️ Some tests failed. Please check the configuration and try again.");
    }

    // Recommendations
    console.log("\n💡 RECOMMENDATIONS");
    console.log("-".repeat(20));
    
    if (connectionTest.success) {
      console.log("✅ SSH connection is working");
    } else {
      console.log("❌ Check SSH credentials and network connectivity");
    }

    if (ttsResult.success && ttsResult.sshCopy?.success) {
      console.log("✅ TTS + SSH copy integration is working");
      console.log(`   Remote file: ${ttsResult.sshCopy.remoteFilePath}`);
      console.log(`   Transfer time: ${ttsResult.sshCopy.transferTime}ms`);
    } else {
      console.log("❌ TTS + SSH copy integration needs attention");
      if (ttsResult.sshCopy?.error) {
        console.log(`   Error: ${ttsResult.sshCopy.error}`);
      }
    }

    console.log("\n🔧 NEXT STEPS");
    console.log("-".repeat(15));
    console.log("1. If all tests pass, the system is ready for production");
    console.log("2. Test with actual phone calls to verify audio playback");
    console.log("3. Monitor logs for any SSH copy failures in production");
    console.log("4. Consider setting up SSH key authentication for better security");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
  } finally {
    // Stop broker
    await broker.stop();
    console.log("\n🛑 Broker stopped");
  }
}

// Helper function to check environment variables
function checkEnvironmentVariables() {
  console.log("\n🔍 Environment Variables Check");
  console.log("-".repeat(35));

  const requiredVars = [
    'SSH_HOST',
    'SSH_USERNAME', 
    'SSH_PASSWORD',
    'OPENAI_API_KEY'
  ];

  const optionalVars = [
    'SSH_PORT',
    'REMOTE_SOUNDS_DIR',
    'ASTERISK_SOUNDS_DIR'
  ];

  console.log("Required variables:");
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      const displayValue = varName.includes('PASSWORD') || varName.includes('KEY')
        ? `${value.substring(0, 8)}...`
        : value;
      console.log(`  ✅ ${varName}: ${displayValue}`);
    } else {
      console.log(`  ❌ ${varName}: Not set`);
    }
  });

  console.log("\nOptional variables:");
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: ${value}`);
    } else {
      console.log(`  ⚪ ${varName}: Using default`);
    }
  });
}

// Main execution
async function main() {
  console.log("🚀 SSH Copy Test Suite");
  console.log("=".repeat(50));
  
  // Check environment variables first
  checkEnvironmentVariables();
  
  // Run tests
  await testSSHCopy();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testSSHCopy, checkEnvironmentVariables };
