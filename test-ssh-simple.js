#!/usr/bin/env node

/**
 * Simple SSH connection test
 * Tests basic SSH connectivity to Asterisk server
 */

const { NodeSSH } = require('node-ssh');
const fs = require('fs');
const path = require('path');

// SSH Configuration
const SSH_CONFIG = {
  host: process.env.SSH_HOST || "***************",
  username: process.env.SSH_USERNAME || "root",
  password: process.env.SSH_PASSWORD || "Ey5WrsNDHynyEb4uawGO6hxWzIvOslTH",
  port: process.env.SSH_PORT || 22,
  readyTimeout: 30000
};

const REMOTE_SOUNDS_DIR = process.env.REMOTE_SOUNDS_DIR || "/var/lib/asterisk/sounds";

async function testSSHConnection() {
  console.log("🔍 Testing SSH Connection to Asterisk Server");
  console.log("=".repeat(50));
  
  console.log("Configuration:");
  console.log(`  Host: ${SSH_CONFIG.host}`);
  console.log(`  Username: ${SSH_CONFIG.username}`);
  console.log(`  Port: ${SSH_CONFIG.port}`);
  console.log(`  Remote sounds dir: ${REMOTE_SOUNDS_DIR}`);
  console.log("");

  const ssh = new NodeSSH();

  try {
    // Test 1: Basic connection
    console.log("📡 Test 1: Basic SSH Connection");
    console.log("-".repeat(35));
    
    await ssh.connect(SSH_CONFIG);
    console.log("✅ SSH connection successful!");

    // Test 2: Check remote directory
    console.log("\n📁 Test 2: Check Remote Directory");
    console.log("-".repeat(40));
    
    const lsResult = await ssh.execCommand(`ls -la ${REMOTE_SOUNDS_DIR}`);
    console.log(`Command: ls -la ${REMOTE_SOUNDS_DIR}`);
    console.log(`Exit code: ${lsResult.code}`);
    
    if (lsResult.code === 0) {
      console.log("✅ Remote directory exists and is accessible");
      console.log("Directory contents:");
      console.log(lsResult.stdout);
    } else {
      console.log("❌ Remote directory check failed");
      console.log("Error:", lsResult.stderr);
    }

    // Test 3: Check/Create voicebot subdirectory
    console.log("\n📂 Test 3: Check/Create Voicebot Directory");
    console.log("-".repeat(50));
    
    const voicebotDir = `${REMOTE_SOUNDS_DIR}/voicebot`;
    const checkVoicebotResult = await ssh.execCommand(`ls -la ${voicebotDir}`);
    
    if (checkVoicebotResult.code === 0) {
      console.log("✅ Voicebot directory already exists");
      console.log(checkVoicebotResult.stdout);
    } else {
      console.log("📁 Creating voicebot directory...");
      const createResult = await ssh.execCommand(`mkdir -p ${voicebotDir} && chmod 755 ${voicebotDir}`);
      
      if (createResult.code === 0) {
        console.log("✅ Voicebot directory created successfully");
      } else {
        console.log("❌ Failed to create voicebot directory");
        console.log("Error:", createResult.stderr);
      }
    }

    // Test 4: Test file upload (create a small test file)
    console.log("\n📤 Test 4: Test File Upload");
    console.log("-".repeat(35));
    
    const testFileName = `ssh_test_${Date.now()}.txt`;
    const testFilePath = path.join(__dirname, testFileName);
    const testContent = `SSH test file created at ${new Date().toISOString()}`;
    
    // Create local test file
    fs.writeFileSync(testFilePath, testContent);
    console.log(`Created local test file: ${testFilePath}`);
    
    // Upload to remote
    const remoteTestPath = `${voicebotDir}/${testFileName}`;
    await ssh.putFile(testFilePath, remoteTestPath);
    console.log(`Uploaded to remote: ${remoteTestPath}`);
    
    // Verify upload
    const verifyResult = await ssh.execCommand(`cat ${remoteTestPath}`);
    if (verifyResult.code === 0 && verifyResult.stdout.trim() === testContent) {
      console.log("✅ File upload and verification successful!");
    } else {
      console.log("❌ File upload verification failed");
      console.log("Expected:", testContent);
      console.log("Got:", verifyResult.stdout);
    }
    
    // Cleanup
    fs.unlinkSync(testFilePath);
    await ssh.execCommand(`rm -f ${remoteTestPath}`);
    console.log("🧹 Cleanup completed");

    // Test 5: Check disk space
    console.log("\n💾 Test 5: Check Disk Space");
    console.log("-".repeat(30));
    
    const dfResult = await ssh.execCommand(`df -h ${REMOTE_SOUNDS_DIR}`);
    if (dfResult.code === 0) {
      console.log("Disk space information:");
      console.log(dfResult.stdout);
    } else {
      console.log("❌ Could not check disk space");
    }

    // Test 6: Check permissions
    console.log("\n🔐 Test 6: Check Permissions");
    console.log("-".repeat(35));
    
    const permResult = await ssh.execCommand(`ls -ld ${REMOTE_SOUNDS_DIR} ${voicebotDir}`);
    if (permResult.code === 0) {
      console.log("Directory permissions:");
      console.log(permResult.stdout);
    } else {
      console.log("❌ Could not check permissions");
    }

    console.log("\n" + "=".repeat(50));
    console.log("🎉 SSH Connection Test Completed Successfully!");
    console.log("=".repeat(50));
    console.log("✅ SSH connection is working");
    console.log("✅ Remote directory is accessible");
    console.log("✅ File upload/download works");
    console.log("✅ Ready for TTS file copying");

  } catch (error) {
    console.error("\n❌ SSH Connection Test Failed:");
    console.error("Error:", error.message);
    
    console.log("\n🔧 Troubleshooting Tips:");
    console.log("1. Check if SSH credentials are correct");
    console.log("2. Verify network connectivity to the server");
    console.log("3. Ensure SSH service is running on the server");
    console.log("4. Check if firewall allows SSH connections");
    console.log("5. Verify the remote directory exists and has proper permissions");
    
  } finally {
    await ssh.dispose();
    console.log("\n🔌 SSH connection closed");
  }
}

// Run the test
if (require.main === module) {
  testSSHConnection().catch(console.error);
}

module.exports = { testSSHConnection };
