#!/usr/bin/env node

/**
 * Simple test for TTS + SSH integration
 */

const { ServiceBroker } = require("moleculer");

// Create service broker
const broker = new ServiceBroker({
  logger: true,
  logLevel: "info"
});

async function testTTSWithSSH() {
  try {
    console.log("🚀 Starting TTS + SSH Test");
    
    // Load services
    broker.loadService("./services/ssh/ssh.service.js");
    broker.loadService("./services/openai/tts/tts.service.js");
    
    // Start broker
    await broker.start();
    console.log("✅ Services started");

    // Test TTS with SSH copy
    console.log("\n🎵 Testing TTS with SSH copy...");
    const result = await broker.call("tts.textToSpeech", {
      text: "Xin chào! Đây là test SSH copy từ TTS service.",
      voice: "alloy",
      forAsterisk: true
    });

    console.log("\n📊 TTS Result:");
    console.log("Success:", result.success);
    console.log("Sound name:", result.soundName);
    console.log("Local path:", result.asteriskPath);
    
    if (result.sshCopy) {
      console.log("\n🚀 SSH Copy Result:");
      console.log("Success:", result.sshCopy.success);
      console.log("Remote path:", result.sshCopy.remoteFilePath);
      console.log("Transfer time:", result.sshCopy.transferTime + "ms");
      if (result.sshCopy.error) {
        console.log("Error:", result.sshCopy.error);
      }
    } else {
      console.log("❌ No SSH copy information");
    }

    if (result.success && result.sshCopy?.success) {
      console.log("\n🎉 SUCCESS: TTS + SSH copy working perfectly!");
      console.log(`File is ready on Asterisk server: ${result.sshCopy.remoteFilePath}`);
    } else {
      console.log("\n❌ FAILED: Check the logs above for errors");
    }

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await broker.stop();
    console.log("\n🛑 Services stopped");
  }
}

testTTSWithSSH();
