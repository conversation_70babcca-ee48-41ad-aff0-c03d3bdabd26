"use strict";

const { ServiceBroker } = require("moleculer");
const UsersService = require("../../../services/users/users.service");
const { MoleculerClientError } = require("moleculer").Errors;

describe("Test 'users' service", () => {
  let broker = new ServiceBroker({ logger: false });
  broker.createService(UsersService);

  beforeAll(() => broker.start());
  afterAll(() => broker.stop());

  describe("Test 'users.login' action", () => {
    it("should throw error if email is not provided", async () => {
      expect.assertions(1);
      try {
        await broker.call("users.login", { password: "password" });
      } catch (error) {
        expect(error).toBeInstanceOf(MoleculerClientError);
      }
    });

    it("should throw error if password is not provided", async () => {
      expect.assertions(1);
      try {
        await broker.call("users.login", { email: "<EMAIL>" });
      } catch (error) {
        expect(error).toBeInstanceOf(MoleculerClientError);
      }
    });
  });

  describe("Test 'users.register' action", () => {
    it("should throw error if email is not provided", async () => {
      expect.assertions(1);
      try {
        await broker.call("users.register", { fullName: "Test User", password: "password" });
      } catch (error) {
        expect(error).toBeInstanceOf(MoleculerClientError);
      }
    });

    it("should throw error if fullName is not provided", async () => {
      expect.assertions(1);
      try {
        await broker.call("users.register", { email: "<EMAIL>", password: "password" });
      } catch (error) {
        expect(error).toBeInstanceOf(MoleculerClientError);
      }
    });

    it("should throw error if password is not provided", async () => {
      expect.assertions(1);
      try {
        await broker.call("users.register", { email: "<EMAIL>", fullName: "Test User" });
      } catch (error) {
        expect(error).toBeInstanceOf(MoleculerClientError);
      }
    });
  });
});
